{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5212, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5212, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5212, "tid": 1121, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5212, "tid": 1121, "ts": 1751407133184732, "dur": 1134, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5212, "tid": 1121, "ts": 1751407133191227, "dur": 1132, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5212, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5212, "tid": 1, "ts": 1751407132495486, "dur": 6521, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751407132502013, "dur": 80909, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751407132582933, "dur": 56729, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5212, "tid": 1121, "ts": 1751407133192366, "dur": 18, "ph": "X", "name": "", "args": {}}, {"pid": 5212, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132492752, "dur": 11974, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132504731, "dur": 667150, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132506202, "dur": 2922, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132509133, "dur": 1605, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132510742, "dur": 344, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511089, "dur": 15, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511105, "dur": 86, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511194, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511196, "dur": 51, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511251, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511254, "dur": 121, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511385, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511387, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511450, "dur": 2, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511453, "dur": 45, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511502, "dur": 1, "ph": "X", "name": "ProcessMessages 105", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511505, "dur": 49, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511557, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511559, "dur": 57, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511620, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511622, "dur": 24, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511648, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511651, "dur": 67, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511722, "dur": 1, "ph": "X", "name": "ProcessMessages 119", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511725, "dur": 130, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511860, "dur": 3, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511865, "dur": 95, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511967, "dur": 2, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132511971, "dur": 91, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132512066, "dur": 2, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132512070, "dur": 214, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132512288, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132512290, "dur": 499, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132512793, "dur": 2, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132512796, "dur": 104, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132512904, "dur": 6, "ph": "X", "name": "ProcessMessages 4322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132512911, "dur": 46, "ph": "X", "name": "ReadAsync 4322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132512960, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513021, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513023, "dur": 49, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513075, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513077, "dur": 44, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513125, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513169, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513171, "dur": 35, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513210, "dur": 39, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513252, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513254, "dur": 48, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513305, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513307, "dur": 45, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513355, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513358, "dur": 44, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513404, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513405, "dur": 36, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513444, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513446, "dur": 34, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513483, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513485, "dur": 36, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513523, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513524, "dur": 30, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513558, "dur": 31, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513593, "dur": 38, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513642, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513687, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513689, "dur": 49, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513742, "dur": 39, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513784, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513786, "dur": 38, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513826, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513828, "dur": 35, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513865, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513867, "dur": 34, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513905, "dur": 36, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513943, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513945, "dur": 40, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513987, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132513989, "dur": 36, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514027, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514029, "dur": 39, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514071, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514072, "dur": 30, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514105, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514106, "dur": 31, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514141, "dur": 37, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514181, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514183, "dur": 38, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514224, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514226, "dur": 36, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514267, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514320, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514322, "dur": 47, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514372, "dur": 37, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514412, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514414, "dur": 38, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514455, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514456, "dur": 41, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514500, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514501, "dur": 39, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514543, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514545, "dur": 38, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514585, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514587, "dur": 38, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514627, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514629, "dur": 32, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514663, "dur": 1, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514665, "dur": 32, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514700, "dur": 40, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514743, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514745, "dur": 36, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514785, "dur": 37, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514825, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514826, "dur": 37, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514865, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514867, "dur": 35, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514904, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514906, "dur": 38, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514946, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514948, "dur": 37, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514987, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132514988, "dur": 37, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515028, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515030, "dur": 38, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515071, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515072, "dur": 38, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515113, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515114, "dur": 32, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515149, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515151, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515188, "dur": 36, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515227, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515230, "dur": 43, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515278, "dur": 46, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515327, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515329, "dur": 45, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515378, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515417, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515419, "dur": 32, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515456, "dur": 37, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515496, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515498, "dur": 38, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515538, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515540, "dur": 37, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515579, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515581, "dur": 37, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515620, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515622, "dur": 37, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515662, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515663, "dur": 30, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515697, "dur": 50, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515751, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515793, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515794, "dur": 37, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515834, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515836, "dur": 38, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515876, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515878, "dur": 39, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515919, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515921, "dur": 31, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515956, "dur": 40, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132515998, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516000, "dur": 39, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516041, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516043, "dur": 38, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516085, "dur": 37, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516124, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516127, "dur": 32, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516163, "dur": 30, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516197, "dur": 41, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516240, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516243, "dur": 46, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516292, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516294, "dur": 45, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516341, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516343, "dur": 36, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516381, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516383, "dur": 168, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516555, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516600, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516602, "dur": 40, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516645, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516647, "dur": 38, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516688, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516690, "dur": 46, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516738, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516740, "dur": 32, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516776, "dur": 34, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516814, "dur": 40, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516856, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516858, "dur": 37, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516897, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516899, "dur": 38, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516939, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516941, "dur": 34, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132516979, "dur": 37, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517020, "dur": 38, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517060, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517063, "dur": 39, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517104, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517106, "dur": 40, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517149, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517150, "dur": 33, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517186, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517188, "dur": 60, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517253, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517304, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517307, "dur": 43, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517352, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517353, "dur": 39, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517394, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517396, "dur": 30, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517429, "dur": 32, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517464, "dur": 33, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517499, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517501, "dur": 33, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517536, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517537, "dur": 34, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517574, "dur": 31, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517608, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517609, "dur": 30, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517643, "dur": 49, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517696, "dur": 44, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517742, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517744, "dur": 36, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517783, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517784, "dur": 35, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517822, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517824, "dur": 32, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517858, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517860, "dur": 37, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517899, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517902, "dur": 36, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517940, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517942, "dur": 34, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517979, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132517981, "dur": 31, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518014, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518015, "dur": 38, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518055, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518057, "dur": 27, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518086, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518088, "dur": 38, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518129, "dur": 35, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518166, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518168, "dur": 36, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518206, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518208, "dur": 34, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518246, "dur": 2, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518249, "dur": 47, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518298, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518301, "dur": 45, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518348, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518350, "dur": 37, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518390, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518392, "dur": 43, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518438, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518440, "dur": 39, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518483, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518485, "dur": 37, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518524, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518527, "dur": 37, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518568, "dur": 36, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518607, "dur": 35, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518645, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518646, "dur": 35, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518685, "dur": 33, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518720, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518722, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518752, "dur": 37, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518791, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518792, "dur": 37, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518833, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518836, "dur": 49, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518888, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518890, "dur": 39, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518932, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518935, "dur": 36, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518974, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132518975, "dur": 39, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519017, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519021, "dur": 41, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519064, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519066, "dur": 37, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519105, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519107, "dur": 36, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519146, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519148, "dur": 44, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519221, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519223, "dur": 51, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519279, "dur": 2, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519282, "dur": 47, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519332, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519335, "dur": 35, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519373, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519375, "dur": 46, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519423, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519426, "dur": 45, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519473, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519475, "dur": 41, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519519, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519520, "dur": 32, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519555, "dur": 34, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519592, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519593, "dur": 100, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519698, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519701, "dur": 58, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519762, "dur": 2, "ph": "X", "name": "ProcessMessages 1391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519765, "dur": 43, "ph": "X", "name": "ReadAsync 1391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519812, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519814, "dur": 55, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519872, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519875, "dur": 47, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519923, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519925, "dur": 43, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519971, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132519973, "dur": 72, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520050, "dur": 2, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520054, "dur": 86, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520143, "dur": 2, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520147, "dur": 209, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520366, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520369, "dur": 179, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520553, "dur": 2, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520557, "dur": 58, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520618, "dur": 2, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520622, "dur": 57, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520682, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520686, "dur": 57, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520746, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520749, "dur": 48, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520800, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520802, "dur": 44, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520849, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520851, "dur": 42, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520896, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520899, "dur": 44, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520946, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520949, "dur": 46, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132520998, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521000, "dur": 45, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521048, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521051, "dur": 39, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521093, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521095, "dur": 45, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521143, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521145, "dur": 97, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521246, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521248, "dur": 50, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521301, "dur": 2, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521305, "dur": 40, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521348, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521350, "dur": 53, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521405, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521407, "dur": 48, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521457, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521459, "dur": 34, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521497, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521498, "dur": 37, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521538, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521540, "dur": 40, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521583, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521585, "dur": 45, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521632, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521634, "dur": 40, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521677, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521679, "dur": 40, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521721, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521723, "dur": 32, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521757, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521759, "dur": 34, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521796, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521843, "dur": 39, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521885, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521886, "dur": 39, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521928, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521930, "dur": 38, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521971, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132521974, "dur": 38, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522015, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522017, "dur": 43, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522062, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522064, "dur": 40, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522107, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522109, "dur": 33, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522167, "dur": 46, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522216, "dur": 231, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522450, "dur": 93, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522547, "dur": 5, "ph": "X", "name": "ProcessMessages 3569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522553, "dur": 44, "ph": "X", "name": "ReadAsync 3569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522600, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522602, "dur": 48, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522653, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522656, "dur": 46, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522705, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522708, "dur": 47, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522757, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522759, "dur": 36, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522798, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522799, "dur": 42, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522845, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522846, "dur": 35, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522884, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522885, "dur": 35, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522925, "dur": 32, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522960, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132522961, "dur": 58, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523023, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523025, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523068, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523070, "dur": 38, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523110, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523111, "dur": 34, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523147, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523148, "dur": 34, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523186, "dur": 71, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523260, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523262, "dur": 47, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523312, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523315, "dur": 48, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523366, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523368, "dur": 42, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523412, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523415, "dur": 33, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523450, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523451, "dur": 35, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523490, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523530, "dur": 28, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523561, "dur": 43, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523607, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523609, "dur": 44, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523656, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523657, "dur": 37, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523698, "dur": 34, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523734, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523736, "dur": 30, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523769, "dur": 35, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523807, "dur": 36, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523848, "dur": 32, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523882, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523884, "dur": 36, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523923, "dur": 40, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523965, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132523968, "dur": 30, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524001, "dur": 38, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524042, "dur": 34, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524078, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524080, "dur": 146, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524228, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524230, "dur": 65, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524299, "dur": 3, "ph": "X", "name": "ProcessMessages 1617", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524303, "dur": 51, "ph": "X", "name": "ReadAsync 1617", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524357, "dur": 3, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524362, "dur": 42, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524406, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524408, "dur": 39, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524449, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524450, "dur": 40, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524493, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524497, "dur": 47, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524547, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524549, "dur": 38, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524589, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524590, "dur": 33, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524627, "dur": 37, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524666, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524668, "dur": 29, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524700, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524740, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524741, "dur": 37, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524780, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524782, "dur": 32, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524817, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524820, "dur": 46, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524869, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524872, "dur": 38, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524913, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524915, "dur": 35, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524953, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524954, "dur": 35, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132524993, "dur": 30, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525027, "dur": 36, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525065, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525066, "dur": 28, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525096, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525098, "dur": 30, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525130, "dur": 8, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525139, "dur": 37, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525179, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525180, "dur": 46, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525230, "dur": 40, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525273, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525275, "dur": 48, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525327, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525330, "dur": 49, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525381, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525383, "dur": 38, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525423, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525425, "dur": 32, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525460, "dur": 34, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525497, "dur": 36, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525536, "dur": 28, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525568, "dur": 34, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525605, "dur": 36, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525645, "dur": 35, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525682, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525683, "dur": 36, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525723, "dur": 36, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525763, "dur": 34, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525799, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525801, "dur": 37, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525841, "dur": 34, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525877, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525880, "dur": 38, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525922, "dur": 29, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525954, "dur": 35, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525991, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132525993, "dur": 32, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526027, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526029, "dur": 29, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526061, "dur": 35, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526098, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526099, "dur": 31, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526133, "dur": 34, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526170, "dur": 30, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526205, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526249, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526251, "dur": 39, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526293, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526296, "dur": 35, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526334, "dur": 35, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526371, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526372, "dur": 36, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526417, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526418, "dur": 28, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526450, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526538, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526540, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526587, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526589, "dur": 37, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526629, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526631, "dur": 86, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526721, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526759, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526761, "dur": 42, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526806, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526807, "dur": 38, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526847, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526849, "dur": 86, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526940, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526988, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132526989, "dur": 36, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527027, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527029, "dur": 30, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527062, "dur": 75, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527141, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527189, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527191, "dur": 53, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527247, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527249, "dur": 35, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527287, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527289, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527352, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527396, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527398, "dur": 40, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527440, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527442, "dur": 31, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527476, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527478, "dur": 63, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527545, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527593, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527596, "dur": 35, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527634, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527636, "dur": 43, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527682, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527684, "dur": 70, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527762, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527806, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527808, "dur": 45, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527856, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527858, "dur": 38, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132527899, "dur": 145, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528050, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528098, "dur": 3, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528103, "dur": 41, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528148, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528151, "dur": 44, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528199, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528201, "dur": 105, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528311, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528418, "dur": 3, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528423, "dur": 137, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528568, "dur": 3, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528574, "dur": 215, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528794, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528796, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528849, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528852, "dur": 66, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528922, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528924, "dur": 37, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528965, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132528968, "dur": 109, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529083, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529167, "dur": 2, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529171, "dur": 40, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529213, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529216, "dur": 96, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529315, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529318, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529369, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529372, "dur": 45, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529420, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529422, "dur": 82, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529508, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529559, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529561, "dur": 44, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529608, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529611, "dur": 38, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529652, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529654, "dur": 88, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529746, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529749, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529801, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529803, "dur": 43, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529849, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529851, "dur": 86, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529940, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529942, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529986, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132529989, "dur": 40, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530032, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530034, "dur": 30, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530068, "dur": 67, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530139, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530188, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530190, "dur": 42, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530234, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530237, "dur": 36, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530276, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530277, "dur": 66, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530348, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530396, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530399, "dur": 43, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530444, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530446, "dur": 29, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530478, "dur": 68, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530552, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530597, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530599, "dur": 40, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530642, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530644, "dur": 32, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530678, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530680, "dur": 64, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530747, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530795, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530797, "dur": 41, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530840, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530843, "dur": 37, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530883, "dur": 28, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530915, "dur": 69, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132530987, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531024, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531026, "dur": 33, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531061, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531063, "dur": 34, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531099, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531100, "dur": 34, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531138, "dur": 37, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531176, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531178, "dur": 31, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531213, "dur": 38, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531253, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531255, "dur": 42, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531300, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531302, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531382, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531427, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531429, "dur": 36, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531467, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531469, "dur": 28, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531500, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531502, "dur": 80, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531586, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531629, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531631, "dur": 40, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531673, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531675, "dur": 31, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531709, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531711, "dur": 84, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531799, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531847, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531849, "dur": 40, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531893, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531896, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132531972, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532014, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532016, "dur": 37, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532057, "dur": 37, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532096, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532099, "dur": 36, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532137, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532139, "dur": 41, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532182, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532184, "dur": 37, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532224, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532226, "dur": 42, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532271, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532273, "dur": 78, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532356, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532401, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532405, "dur": 42, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532449, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532451, "dur": 30, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532486, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532488, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532555, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532600, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532602, "dur": 36, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532641, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532643, "dur": 34, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532680, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532682, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532748, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532793, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532795, "dur": 44, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532842, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532844, "dur": 40, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532886, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532888, "dur": 37, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532927, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532929, "dur": 36, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532966, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132532969, "dur": 31, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533005, "dur": 31, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533040, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533124, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533163, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533166, "dur": 59, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533228, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533230, "dur": 44, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533277, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533280, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533321, "dur": 40, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533363, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533365, "dur": 37, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533404, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533407, "dur": 29, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533440, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533505, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533543, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533546, "dur": 34, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533582, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533584, "dur": 32, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533621, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533697, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533736, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533738, "dur": 33, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533774, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533776, "dur": 32, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533812, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533889, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533927, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533928, "dur": 36, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533967, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132533969, "dur": 36, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534009, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534046, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534047, "dur": 76, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534127, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534167, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534169, "dur": 56, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534228, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534230, "dur": 41, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534274, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534277, "dur": 48, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534330, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534376, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534379, "dur": 39, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534420, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534422, "dur": 81, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534507, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534551, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534553, "dur": 40, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534595, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534597, "dur": 32, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534631, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534633, "dur": 62, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534700, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534739, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534741, "dur": 39, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534783, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534785, "dur": 31, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534819, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534822, "dur": 65, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534890, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534932, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534934, "dur": 41, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534978, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132534979, "dur": 30, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535012, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535014, "dur": 64, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535082, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535125, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535128, "dur": 47, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535178, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535180, "dur": 80, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535266, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535317, "dur": 1, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535320, "dur": 39, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535361, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535363, "dur": 75, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535443, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535484, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535486, "dur": 39, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535528, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535530, "dur": 42, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535574, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535576, "dur": 70, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535650, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535692, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535694, "dur": 42, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535739, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535741, "dur": 32, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535776, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535779, "dur": 58, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535839, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535841, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535884, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535887, "dur": 41, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535930, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535931, "dur": 30, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132535966, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536033, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536076, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536077, "dur": 40, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536120, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536123, "dur": 31, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536156, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536158, "dur": 77, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536238, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536240, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536345, "dur": 2, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536349, "dur": 40, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536392, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536395, "dur": 72, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536473, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536531, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536534, "dur": 66, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536602, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536605, "dur": 42, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536650, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536652, "dur": 200, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536873, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536877, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536994, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132536998, "dur": 56, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537059, "dur": 3, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537065, "dur": 340, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537410, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537413, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537473, "dur": 2, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537476, "dur": 45, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537525, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537530, "dur": 27, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537560, "dur": 126, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537692, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537742, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537745, "dur": 46, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537794, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537796, "dur": 38, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537836, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537839, "dur": 84, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537927, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537967, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132537969, "dur": 34, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538005, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538007, "dur": 78, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538088, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538126, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538128, "dur": 31, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538162, "dur": 68, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538233, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538235, "dur": 56, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538294, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538297, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538346, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538348, "dur": 41, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538391, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538393, "dur": 32, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538429, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538499, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538538, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538540, "dur": 33, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538575, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538577, "dur": 31, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538610, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538611, "dur": 87, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538702, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538739, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538741, "dur": 32, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538777, "dur": 32, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538811, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538813, "dur": 84, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538901, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538939, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538940, "dur": 32, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132538976, "dur": 32, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539010, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539012, "dur": 85, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539100, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539137, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539140, "dur": 32, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539176, "dur": 37, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539217, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539220, "dur": 45, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539268, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539270, "dur": 73, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539346, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539388, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539389, "dur": 35, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539427, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539429, "dur": 32, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539463, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539464, "dur": 35, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539501, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539503, "dur": 33, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539540, "dur": 34, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539578, "dur": 29, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539610, "dur": 31, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539645, "dur": 28, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539677, "dur": 68, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539747, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539749, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539787, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539789, "dur": 31, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539823, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539825, "dur": 33, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539860, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539863, "dur": 36, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539903, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539906, "dur": 47, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539957, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132539959, "dur": 44, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540006, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540009, "dur": 37, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540050, "dur": 33, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540086, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540163, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540226, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540229, "dur": 51, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540284, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540286, "dur": 43, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540333, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540334, "dur": 36, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540374, "dur": 33, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540411, "dur": 27, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540441, "dur": 31, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540476, "dur": 96, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540576, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540610, "dur": 96, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540710, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540714, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132540756, "dur": 419, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541178, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541290, "dur": 11, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541304, "dur": 38, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541345, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541348, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541371, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541373, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541410, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541412, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541453, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541456, "dur": 41, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541500, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541503, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541545, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541548, "dur": 45, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541597, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541600, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541650, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541653, "dur": 37, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541693, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541696, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541734, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541737, "dur": 43, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541782, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541785, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541823, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541827, "dur": 39, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541869, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541872, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541909, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541911, "dur": 33, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541948, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541950, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541986, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132541988, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542023, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542026, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542062, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542065, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542102, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542105, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542144, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542146, "dur": 47, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542197, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542200, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542242, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542246, "dur": 38, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542288, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542291, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542327, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542329, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542370, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542373, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542410, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542412, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542440, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542442, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542474, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542476, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542514, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542517, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542559, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542562, "dur": 34, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542599, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542602, "dur": 51, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542656, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542658, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542697, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542699, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542739, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542744, "dur": 39, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542785, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542789, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542826, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542829, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542868, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542873, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542912, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542916, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542952, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542955, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542994, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132542997, "dur": 40, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543040, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543042, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543078, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543080, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543127, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543130, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543177, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543181, "dur": 50, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543234, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543237, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543270, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543273, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543313, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543315, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543359, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543363, "dur": 43, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543410, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543413, "dur": 36, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543452, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543455, "dur": 40, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543498, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543501, "dur": 53, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543556, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543560, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543611, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543615, "dur": 45, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543662, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543666, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543706, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543709, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543754, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543758, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543809, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543812, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543859, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543861, "dur": 84, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543948, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543951, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132543997, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544034, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544036, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544073, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544076, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544111, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544113, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544156, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544159, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544222, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544225, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544269, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544272, "dur": 48, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544323, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544326, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544372, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544375, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544414, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544417, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544455, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544457, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544494, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544497, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544534, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544537, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544564, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544566, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544598, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544600, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544694, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544740, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544743, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544765, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544767, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544825, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544867, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544870, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544909, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544911, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544952, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544955, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544991, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132544993, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545076, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545080, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545165, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545177, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545252, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545255, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545328, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545367, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545373, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545578, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545581, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545624, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545627, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545679, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545681, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545731, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545733, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545772, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545806, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545808, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545854, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545858, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545897, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545900, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545943, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132545946, "dur": 54, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546003, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546005, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546047, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546050, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546089, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546092, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546134, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546137, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546177, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546179, "dur": 228, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546411, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546414, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546450, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546452, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546694, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546697, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546733, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546735, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546931, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546935, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546973, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132546975, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132547012, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132547014, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132547203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132547206, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132547245, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132547247, "dur": 5088, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132552342, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132552345, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132552417, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132552420, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132552466, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132552468, "dur": 42, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132552514, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132552516, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132552553, "dur": 2116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132554677, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132554680, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132554750, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132554754, "dur": 918, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132555675, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132555678, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132555720, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132555723, "dur": 194, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132555923, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132555961, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132555963, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132555997, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556001, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556032, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556074, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556076, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556114, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556117, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556149, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556152, "dur": 383, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556539, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556541, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556577, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556580, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556729, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556731, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556767, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556772, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556951, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556987, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132556990, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557151, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557186, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557188, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557244, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557247, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557326, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557328, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557368, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557370, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557542, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557579, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557582, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557620, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557622, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557659, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557661, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557765, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557802, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557805, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557841, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557844, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557880, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132557883, "dur": 276, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558162, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558164, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558202, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558205, "dur": 183, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558393, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558432, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558434, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558471, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558474, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558509, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558511, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558546, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558548, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558587, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558590, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558670, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558705, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558708, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558745, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558747, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558785, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558787, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558841, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558877, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132558879, "dur": 162, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559047, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559083, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559149, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559181, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559300, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559302, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559336, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559339, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559373, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559376, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559450, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559452, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559485, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559489, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559521, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559523, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559558, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559560, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559601, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559604, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559646, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559648, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559733, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559735, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559770, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559773, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559811, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559813, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559846, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559849, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559987, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132559989, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560025, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560029, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560112, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560114, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560148, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560151, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560190, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560193, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560231, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560233, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560334, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560336, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560372, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560374, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560422, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560464, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560466, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560529, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560560, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560563, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560677, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560680, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560731, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560733, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560771, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560774, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560841, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560843, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560879, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560881, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132560976, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561013, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561016, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561056, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561059, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561095, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561097, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561151, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561177, "dur": 737, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561924, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132561928, "dur": 166, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562098, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562101, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562340, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562343, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562384, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562387, "dur": 158, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562553, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562592, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562596, "dur": 391, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562993, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132562995, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563029, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563031, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563092, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563132, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563135, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563179, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563182, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563218, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563221, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563257, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563259, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563294, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563297, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563330, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563333, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563417, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563419, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132563462, "dur": 755, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564222, "dur": 49, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564274, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564277, "dur": 328, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564611, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564668, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564671, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564714, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564750, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564753, "dur": 217, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132564976, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565015, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565019, "dur": 87, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565111, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565155, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565158, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565214, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565249, "dur": 507, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565759, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565761, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565802, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565805, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565841, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565981, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132565983, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566016, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566018, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566138, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566173, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566175, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566208, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566242, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566275, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566419, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566464, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566465, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566510, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566512, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566546, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566679, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566681, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566714, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566716, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566858, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566892, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132566894, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567012, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567014, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567052, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567055, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567091, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567133, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567135, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567171, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567173, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567242, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567279, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567282, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567320, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567322, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567358, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567360, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567502, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567504, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567543, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567545, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567630, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567632, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567666, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567668, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567701, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567703, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567799, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567839, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567841, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567898, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567901, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567939, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567995, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132567998, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568037, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568040, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568146, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568186, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568188, "dur": 184, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568375, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568377, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568411, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568414, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568459, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568492, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568494, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568644, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568676, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568678, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568856, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568893, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568928, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568990, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132568992, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569027, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569030, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569063, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569065, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569130, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569134, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569173, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569177, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569277, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569279, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569314, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569317, "dur": 165, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569486, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569488, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569523, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569525, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569554, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569589, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569592, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569769, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569771, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569811, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569813, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569872, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132569874, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132570004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132570007, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132570071, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132570076, "dur": 792, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132570875, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132570880, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132570931, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132570934, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571004, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571041, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571044, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571224, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571226, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571283, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571286, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571405, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571408, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571448, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571451, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571497, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571501, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571551, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571592, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571595, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571636, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571640, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571682, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571684, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571798, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571800, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571840, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132571842, "dur": 576, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572425, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572428, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572476, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572479, "dur": 163, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572646, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572648, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572706, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572709, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572835, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572836, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572875, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132572878, "dur": 178, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573060, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573062, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573108, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573110, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573153, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573155, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573335, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573386, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573388, "dur": 158, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573551, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573605, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573607, "dur": 291, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573902, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573904, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573951, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132573953, "dur": 210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132574169, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132574171, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132574211, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132574214, "dur": 1210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132575430, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132575433, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132575485, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132575491, "dur": 134787, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132710289, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132710293, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132710362, "dur": 2677, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132713049, "dur": 4557, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132717616, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132717621, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132717676, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132717682, "dur": 219, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132717904, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132717906, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132717941, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132717944, "dur": 573, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132718520, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132718522, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132718557, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132718560, "dur": 355, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132718920, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719036, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719038, "dur": 348, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719390, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719393, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719433, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719435, "dur": 438, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719880, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719919, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719921, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719975, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132719977, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132720000, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132720002, "dur": 380, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132720388, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132720390, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132720432, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132720435, "dur": 1596, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722050, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722058, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722190, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722195, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722311, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722314, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722359, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722362, "dur": 182, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722548, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722550, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722593, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722596, "dur": 256, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722855, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722857, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722894, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132722896, "dur": 404, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132723309, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132723341, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132723344, "dur": 1092, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724442, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724480, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724482, "dur": 344, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724829, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724831, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724867, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724869, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724947, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724986, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132724988, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132725185, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132725217, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132725219, "dur": 636, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132725861, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132725893, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132725895, "dur": 545, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726448, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726494, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726496, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726678, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726715, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726717, "dur": 134, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726853, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726856, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726888, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132726890, "dur": 509, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132727403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132727405, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132727451, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132727453, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132727586, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132727618, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132727621, "dur": 1400, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729026, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729029, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729074, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729077, "dur": 519, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729604, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729649, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729652, "dur": 313, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729968, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132729971, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730021, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730024, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730060, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730063, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730142, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730145, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730178, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730181, "dur": 441, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730629, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730667, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730669, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730706, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132730708, "dur": 784, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132731496, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132731498, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132731538, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132731541, "dur": 462, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732007, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732009, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732047, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732049, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732328, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732330, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732369, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732372, "dur": 412, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732787, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732789, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732828, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732831, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732906, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732908, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732944, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132732947, "dur": 363, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132733314, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132733316, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132733353, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132733356, "dur": 518, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132733881, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132733918, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132733921, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132733959, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132733999, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132734002, "dur": 405, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132734410, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132734412, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132734436, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132734682, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132734684, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132734729, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132734731, "dur": 721, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132735456, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132735459, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132735503, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132735505, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132735545, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132735547, "dur": 469, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132736021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132736023, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132736076, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132736079, "dur": 496, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132736579, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132736581, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132736637, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132736640, "dur": 402, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132737050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132737053, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132737175, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132737178, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132737219, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132737223, "dur": 719, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132737946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132737949, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132738010, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132738014, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132738118, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132738122, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132738191, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132738194, "dur": 1427, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739629, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739634, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739709, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739714, "dur": 56, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739774, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739777, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739819, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739822, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739866, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739869, "dur": 40, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739913, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739916, "dur": 43, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739964, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132739968, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740002, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740006, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740049, "dur": 2, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740078, "dur": 70, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740151, "dur": 6, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740158, "dur": 39, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740202, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740206, "dur": 39, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740248, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740253, "dur": 38, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740294, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740297, "dur": 41, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740340, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740344, "dur": 46, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740393, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740397, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740444, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132740446, "dur": 144206, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132884659, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132884663, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132884711, "dur": 23, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132884736, "dur": 2563, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132887309, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132887314, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132887381, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407132887385, "dur": 119374, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133006768, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133006772, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133006822, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133006826, "dur": 59, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133006890, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133006893, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133006991, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133006995, "dur": 109258, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133116263, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133116267, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133116322, "dur": 24, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133116348, "dur": 2197, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133118556, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133118561, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133118628, "dur": 29, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133118659, "dur": 6930, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133125601, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133125606, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133125674, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133125677, "dur": 2967, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133128653, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133128657, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133128706, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133128710, "dur": 1750, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133130467, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133130470, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133130534, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133130564, "dur": 28850, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133159425, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133159431, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133159536, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133159542, "dur": 1474, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133161032, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133161035, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133161098, "dur": 27, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133161127, "dur": 495, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133161627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133161630, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133161680, "dur": 397, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407133162081, "dur": 9559, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5212, "tid": 1121, "ts": 1751407133192387, "dur": 3733, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5212, "tid": 8589934592, "ts": 1751407132489172, "dur": 150533, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751407132639709, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751407132639719, "dur": 1567, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5212, "tid": 1121, "ts": 1751407133196124, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5212, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5212, "tid": 4294967296, "ts": 1751407132463348, "dur": 709614, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751407132467317, "dur": 9348, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751407133173250, "dur": 7326, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751407133177497, "dur": 357, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751407133180708, "dur": 24, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5212, "tid": 1121, "ts": 1751407133196138, "dur": 14, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751407132502406, "dur": 2449, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407132504873, "dur": 1553, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407132506583, "dur": 91, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751407132506675, "dur": 394, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407132508488, "dur": 1148, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132510875, "dur": 1742, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132512951, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751407132513010, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751407132513289, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751407132513431, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132513631, "dur": 204, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_2A790542FCF7EA37.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132513839, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_D58562F0DF82BCBA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132514351, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132514585, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132514756, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_7C46D60DEB68F39C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132515209, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751407132515750, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751407132515832, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132516840, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751407132521248, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751407132521537, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132521940, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751407132522235, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751407132523732, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Updater.ref.dll_34B8D5B7DED6E76D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132524016, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132524746, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751407132529882, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751407132538419, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751407132538974, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751407132507101, "dur": 35031, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407132542148, "dur": 620488, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407133162639, "dur": 196, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407133162981, "dur": 101, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407133163156, "dur": 1955, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751407132507406, "dur": 34759, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132542191, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132542778, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_9923BABDF0F3077F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132542879, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132543043, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_92A5F53C232CE1D9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132543512, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132543685, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132543805, "dur": 3595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132547471, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132547588, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751407132547665, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132547859, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132548064, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132548382, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132548714, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132548920, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132549128, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132549358, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132549578, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132549867, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132550072, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132550273, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132550477, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132551041, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132551262, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132551475, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132551693, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132551903, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132552124, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132552350, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132552584, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132552798, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132553045, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132553260, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132553482, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132553690, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132553921, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132554233, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132554487, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132554720, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132554934, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132555352, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132555556, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132555780, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132555994, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132556217, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132556451, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132556667, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132556874, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132557068, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132557276, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132557490, "dur": 2403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132559894, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132560008, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_E8EFDB1B8D5C174E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132560113, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132560303, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132560853, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132561149, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132561370, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132561655, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132562410, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132562543, "dur": 1090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132563686, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132563913, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132564534, "dur": 1681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132566301, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132566546, "dur": 2552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132569099, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132569221, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132569440, "dur": 1562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132571105, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751407132571544, "dur": 1158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132572751, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132572983, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132573124, "dur": 144703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132717832, "dur": 2803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132720636, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132720932, "dur": 2770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132723705, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132723851, "dur": 2062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132725977, "dur": 2198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132728222, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132730490, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751407132730557, "dur": 2421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132733039, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132735420, "dur": 2650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132738118, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132740708, "dur": 459, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751407132741237, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751407132741446, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751407132741668, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751407132741934, "dur": 420634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132507474, "dur": 34722, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132542203, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132542396, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132542782, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132543140, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132543246, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7E2204058CA93257.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132543384, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132543537, "dur": 4892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132548508, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132548746, "dur": 5066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132553850, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132553988, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132554280, "dur": 1861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132556190, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132557272, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132557465, "dur": 2680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132560146, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132560205, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132560282, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132560386, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132560590, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132561323, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751407132561529, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132562223, "dur": 2006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1751407132564272, "dur": 146, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132564968, "dur": 146849, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1751407132716281, "dur": 2764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132719130, "dur": 2146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132721277, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132721517, "dur": 2241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132723761, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132723857, "dur": 2097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132726002, "dur": 2347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132728399, "dur": 2501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132730901, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132731139, "dur": 2322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132733462, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132733550, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132735956, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132738447, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132738582, "dur": 2282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132740868, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407132740967, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407132741248, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751407132741591, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Toonshader.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751407132741883, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751407132741940, "dur": 420627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132507455, "dur": 34727, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132542779, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132542935, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_89B82555D6AF826E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132543295, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132543455, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751407132543625, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132543720, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751407132543900, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132544176, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751407132544799, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751407132545146, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751407132545288, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751407132545387, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132546129, "dur": 1022, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132547152, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751407132547252, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751407132547320, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132547429, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751407132547516, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751407132547714, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132548053, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132548336, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132548646, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132549024, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132549243, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132549463, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132549690, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132549981, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132550195, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132550397, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132550976, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132551186, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132551420, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132551638, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132551892, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132552103, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132552322, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132552548, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132552768, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132553000, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132553213, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132553441, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132553650, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132553859, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132554101, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132554359, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132554567, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132554960, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132555499, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132555900, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132556168, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132556388, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132556620, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132556820, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132557038, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132557245, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132557449, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132557619, "dur": 2325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132560015, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132560213, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132561073, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132561697, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132561910, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132562555, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132564018, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132564092, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132564832, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132564922, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132565093, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132565334, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132565554, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132565989, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132566247, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132566412, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132567079, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132567331, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132567523, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132568040, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132568397, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132569040, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132569736, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132569939, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132570004, "dur": 3047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132573144, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132573337, "dur": 985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132574398, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751407132574590, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132575100, "dur": 141539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132716641, "dur": 3878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132720559, "dur": 3131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132723705, "dur": 2621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132726374, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132728707, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132728946, "dur": 3249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132732237, "dur": 2449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132734687, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132734857, "dur": 2408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132737266, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407132737552, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132737618, "dur": 3719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132741368, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751407132741673, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751407132741910, "dur": 387895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751407133129832, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751407133129807, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751407133130176, "dur": 1812, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751407133131994, "dur": 30604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132507511, "dur": 34699, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132542215, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132542764, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3520FC5D8B4827F4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132542854, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_69C31F9D75BAAB88.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132543286, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132543876, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132543958, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132544215, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132544720, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132544861, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132545005, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132545380, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132545692, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751407132545764, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751407132545909, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132546040, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132546146, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132546272, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132546561, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132546658, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132546760, "dur": 513, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132547315, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132547455, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751407132547635, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132547873, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132548115, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132548457, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132548651, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132548912, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132549124, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132549347, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132549580, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132549849, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132550051, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132550256, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132550461, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132551191, "dur": 1049, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\TextureSamplerState.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751407132552241, "dur": 1508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\Texture3DShaderProperty.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751407132551033, "dur": 2739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132553773, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132554141, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132554396, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132554674, "dur": 618, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\RenderPipeline\\ICloudBackground.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751407132554596, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132555545, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132555820, "dur": 1919, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\DivisionHandler.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751407132555820, "dur": 2393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132558215, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132559085, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132559729, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132559932, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132560696, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132560894, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132561733, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132562004, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132562069, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132562404, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132562690, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132563458, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132563612, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132563890, "dur": 1554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132565521, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132565928, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132566286, "dur": 870, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\CreateWorkspace\\Dialogs\\RepositoriesListView.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751407132566173, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132567608, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132568016, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132568219, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132568886, "dur": 1543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132570430, "dur": 3580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132574012, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751407132574177, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132574659, "dur": 141627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132716291, "dur": 2754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132719121, "dur": 2217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132721381, "dur": 2639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132724021, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132724090, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132726494, "dur": 2401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132728896, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132728963, "dur": 2668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132731687, "dur": 2489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132734177, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132734455, "dur": 2496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132736998, "dur": 2426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407132739485, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407132739641, "dur": 1472, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751407132741146, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751407132741922, "dur": 420670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132507557, "dur": 34673, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132542775, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132542999, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C1FAB9A8897A5958.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751407132543690, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751407132544092, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751407132544319, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751407132544418, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132544938, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751407132545083, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751407132545431, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132545701, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751407132545832, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751407132546229, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751407132546308, "dur": 664, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132546986, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751407132547194, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751407132547518, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751407132547646, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132548208, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132548752, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132549036, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132549257, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132549479, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132549689, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132550148, "dur": 886, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Drawing\\Inspector\\PropertyDrawers\\GradientPropertyDrawer.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751407132549987, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132551068, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132551272, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132551473, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132551679, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132551883, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132552076, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132552298, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132552516, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132552732, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132552992, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132553212, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132553429, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132553636, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132553877, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132554184, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132554582, "dur": 1228, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMPro_EventManager.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751407132554426, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132555867, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132556497, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132556715, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132556922, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132557123, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132557335, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751407132557504, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132558257, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751407132558493, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132559363, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132560124, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751407132560309, "dur": 7139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132567449, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132567734, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751407132567954, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751407132568008, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751407132568797, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132569246, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132569363, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751407132569544, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132570443, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751407132570673, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132571245, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132571354, "dur": 102103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132674829, "dur": 308, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1751407132675138, "dur": 1414, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1751407132676553, "dur": 159, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 5, "ts": 1751407132673458, "dur": 3259, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132676717, "dur": 39566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132716285, "dur": 2773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132719098, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132719447, "dur": 2377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132721827, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132721905, "dur": 2447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132724397, "dur": 2284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132726729, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132729132, "dur": 2210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132731343, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132731511, "dur": 2255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132733767, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132733859, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132736158, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132736224, "dur": 2375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132738600, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751407132738661, "dur": 2447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751407132741175, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751407132741363, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751407132741495, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.FilmInternalUtilities.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751407132741585, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751407132741912, "dur": 420698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132507601, "dur": 34642, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132542248, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CCD605E26FC2B53B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132542407, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132542999, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_FB065B49AB03584B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132543483, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_6AB6B1375B5D7676.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132543758, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_0B080C2548A382FC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132543904, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751407132544061, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751407132544240, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751407132544323, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751407132544448, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751407132544814, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751407132545190, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751407132545434, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132545700, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751407132546236, "dur": 1324, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751407132547639, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751407132547954, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132548611, "dur": 1101, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-synch-l1-2-0.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751407132548404, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132549737, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132550097, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132550297, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132550508, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132551079, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132551290, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132551500, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132551705, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132551918, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132552124, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132552341, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132552559, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132552768, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132553010, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132553220, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132553441, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132553653, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132554069, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132554298, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132554581, "dur": 1266, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Utilities\\CoreRenderPipelinePreferences.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751407132554509, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132555984, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132556330, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132556595, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132556806, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132557023, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132557314, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132557604, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132557668, "dur": 8425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132566138, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132566275, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132566516, "dur": 1197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132567714, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132567809, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132568010, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132568473, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132568588, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132568784, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132569729, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132569952, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132570580, "dur": 4880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132575471, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751407132575715, "dur": 140577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132716294, "dur": 2757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132719052, "dur": 1397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132720449, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132720581, "dur": 4221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132724848, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132727405, "dur": 4719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132732187, "dur": 2965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132735154, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132735554, "dur": 4543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132740141, "dur": 1001, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407132741395, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751407132741733, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751407132741900, "dur": 145829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407132887753, "dur": 116437, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751407132887731, "dur": 118436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407133007656, "dur": 257, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407133008931, "dur": 111149, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751407133129810, "dur": 31071, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751407133129797, "dur": 31086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751407133160907, "dur": 1628, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751407132507649, "dur": 34610, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132542770, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0D58F8E6C2E92DF4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132542865, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_611E434C56DD2756.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132543106, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132543526, "dur": 208, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132543870, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132543993, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751407132544224, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132544366, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751407132544536, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751407132544749, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132545209, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132545385, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132545570, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132545700, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132545801, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132545931, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132546066, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132546244, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132547184, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132547335, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132547489, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751407132547649, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132547892, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132548091, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132548292, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132548524, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132548741, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132548938, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132549155, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132549384, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132549616, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132549914, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132550113, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132550323, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132550527, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132551097, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132551300, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132551510, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132551720, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132551928, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132552135, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132552357, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132552585, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132552919, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132553139, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132553369, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132553575, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132553873, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132554203, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132554463, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132554686, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132555081, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132555617, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132556171, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132556427, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132556658, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132556878, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132557082, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132557326, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132557549, "dur": 1508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132559124, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132559376, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132560090, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132560390, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132560556, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132560879, "dur": 949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132561879, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132562524, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132563298, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132563389, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132563731, "dur": 1363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132565167, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132565393, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132565616, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132565893, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132566146, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132566401, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132566646, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132566700, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132567268, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132567609, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132567856, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132568076, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132568705, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132569772, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132570185, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132570840, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751407132571027, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132572180, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132572455, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132572548, "dur": 104173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132676722, "dur": 40540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132717267, "dur": 2750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132720065, "dur": 3611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132723679, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132723882, "dur": 4059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132727978, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132728039, "dur": 3455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132731495, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132731578, "dur": 2657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132734243, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132734331, "dur": 2680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132737069, "dur": 3728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132740798, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407132740877, "dur": 350, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751407132741501, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751407132741670, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751407132741908, "dur": 384802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407133126740, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751407133126712, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751407133127095, "dur": 35470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132507695, "dur": 34714, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132542755, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_782156EAC64F1FC2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132542837, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132543001, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_82A68248955BDDC4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132543820, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_CE65D8670DA73F5C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132544184, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132544288, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132544536, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132544618, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132544758, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132545070, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751407132545166, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751407132545419, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132545579, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132545772, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132546232, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132546563, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132546655, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132546775, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132546900, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132546974, "dur": 1260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751407132548235, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132548432, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132548628, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132548830, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132549031, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132549250, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132549456, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132549676, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132550337, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132550538, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132551276, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132551480, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132551683, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132551984, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132552200, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132552423, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132552642, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132552837, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132553089, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132553319, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132553527, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132553733, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132554001, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751407132554063, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132554336, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132554757, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132555015, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132555383, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132555597, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132555820, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132556028, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132556564, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132556790, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132556997, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132557199, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132557722, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132558081, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132558697, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132558760, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132558865, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132559074, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132559184, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132559389, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132559950, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132560045, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132560283, "dur": 1199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132561534, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132561707, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132561965, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132562304, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132562506, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132562593, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132563319, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132563413, "dur": 464, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132563880, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132564547, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132564878, "dur": 762, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance\\Runtime\\ResourcesLoader.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751407132564841, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132565990, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132566233, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132566501, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132566757, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132567346, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132567619, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132567816, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132568029, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132568487, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132568604, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132569275, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132569527, "dur": 3328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132572954, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132573020, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132573205, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132574005, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132574200, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132574883, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132575069, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132575457, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751407132575704, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132576827, "dur": 99, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132577874, "dur": 308306, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132888167, "dur": 458, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751407132887724, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751407132888732, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407132888809, "dur": 115371, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751407132888805, "dur": 117365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751407133008158, "dur": 237, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751407133009225, "dur": 108556, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751407133126715, "dur": 351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751407133126702, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751407133127097, "dur": 35538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407133170501, "dur": 2106, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5212, "tid": 1121, "ts": 1751407133196951, "dur": 7949, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5212, "tid": 1121, "ts": 1751407133204984, "dur": 3905, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5212, "tid": 1121, "ts": 1751407133189754, "dur": 20952, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}