{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5212, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5212, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5212, "tid": 1097, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5212, "tid": 1097, "ts": 1751407000276562, "dur": 1254, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5212, "tid": 1097, "ts": 1751407000283051, "dur": 1381, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5212, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5212, "tid": 1, "ts": 1751406998080210, "dur": 8523, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751406998088739, "dur": 83764, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751406998172515, "dur": 68167, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5212, "tid": 1097, "ts": 1751407000284438, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 5212, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998077381, "dur": 31355, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998108741, "dur": 2153319, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998110366, "dur": 2990, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998113366, "dur": 1392, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998114762, "dur": 677, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998115443, "dur": 567, "ph": "X", "name": "ProcessMessages 19812", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116013, "dur": 247, "ph": "X", "name": "ReadAsync 19812", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116264, "dur": 12, "ph": "X", "name": "ProcessMessages 12403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116277, "dur": 56, "ph": "X", "name": "ReadAsync 12403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116336, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116338, "dur": 145, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116488, "dur": 2, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116492, "dur": 70, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116566, "dur": 3, "ph": "X", "name": "ProcessMessages 1222", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116571, "dur": 99, "ph": "X", "name": "ReadAsync 1222", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116673, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116675, "dur": 57, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116735, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116737, "dur": 57, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116798, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116800, "dur": 55, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116857, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116859, "dur": 56, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116917, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116919, "dur": 54, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116979, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998116981, "dur": 65, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117050, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117053, "dur": 51, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117106, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117108, "dur": 38, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117149, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117151, "dur": 36, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117191, "dur": 48, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117243, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117245, "dur": 40, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117288, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117290, "dur": 77, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117371, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117373, "dur": 148, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117526, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117529, "dur": 70, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117602, "dur": 2, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117605, "dur": 62, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117670, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117673, "dur": 42, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117718, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117720, "dur": 130, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117856, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117921, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117923, "dur": 45, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117972, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998117973, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118029, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118031, "dur": 54, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118088, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118091, "dur": 44, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118138, "dur": 1, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118141, "dur": 56, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118200, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118202, "dur": 51, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118256, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118258, "dur": 58, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118320, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118322, "dur": 50, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118375, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118377, "dur": 56, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118438, "dur": 57, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118497, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118500, "dur": 45, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118547, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118549, "dur": 35, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118586, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118588, "dur": 37, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118628, "dur": 53, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118684, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118687, "dur": 54, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118743, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118745, "dur": 40, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118788, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118789, "dur": 40, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118833, "dur": 59, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118896, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118899, "dur": 53, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118955, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998118956, "dur": 46, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119004, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119006, "dur": 44, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119052, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119054, "dur": 35, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119092, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119095, "dur": 56, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119154, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119156, "dur": 46, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119204, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119205, "dur": 39, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119247, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119249, "dur": 40, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119293, "dur": 58, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119354, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119356, "dur": 48, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119407, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119409, "dur": 42, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119456, "dur": 42, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119501, "dur": 41, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119545, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119546, "dur": 48, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119597, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119599, "dur": 54, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119656, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119658, "dur": 43, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119704, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119706, "dur": 41, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119749, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119751, "dur": 56, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119810, "dur": 3, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119815, "dur": 53, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119870, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119872, "dur": 41, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119917, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119918, "dur": 61, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119985, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998119987, "dur": 61, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120051, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120053, "dur": 98, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120158, "dur": 2, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120162, "dur": 68, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120233, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120236, "dur": 68, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120308, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120310, "dur": 47, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120362, "dur": 41, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120406, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120407, "dur": 46, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120457, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120459, "dur": 54, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120517, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120519, "dur": 97, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120619, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120621, "dur": 46, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120671, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120718, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120720, "dur": 37, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120760, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120762, "dur": 78, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120845, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120900, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120902, "dur": 47, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120952, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998120955, "dur": 63, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121021, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121023, "dur": 53, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121080, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121083, "dur": 46, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121131, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121133, "dur": 48, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121186, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121190, "dur": 59, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121252, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121256, "dur": 52, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121311, "dur": 9, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121321, "dur": 52, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121376, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121379, "dur": 54, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121436, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121439, "dur": 66, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121510, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121562, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121564, "dur": 44, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121612, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121615, "dur": 62, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121680, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121682, "dur": 47, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121732, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121734, "dur": 40, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121777, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121778, "dur": 40, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121821, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121824, "dur": 55, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121883, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121886, "dur": 57, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121945, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121947, "dur": 43, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121992, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998121995, "dur": 43, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122040, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122042, "dur": 44, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122090, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122094, "dur": 62, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122162, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122216, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122218, "dur": 41, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122262, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122264, "dur": 47, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122313, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122315, "dur": 41, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122359, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122361, "dur": 62, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122427, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122429, "dur": 53, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122485, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122487, "dur": 44, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122535, "dur": 44, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122585, "dur": 2, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122589, "dur": 56, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122648, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122650, "dur": 43, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122696, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122698, "dur": 39, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122741, "dur": 45, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122788, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122790, "dur": 45, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122838, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122839, "dur": 55, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122897, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122900, "dur": 53, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122956, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998122959, "dur": 44, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123008, "dur": 43, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123055, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123058, "dur": 54, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123114, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123117, "dur": 46, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123167, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123169, "dur": 47, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123218, "dur": 10, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123230, "dur": 51, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123283, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123285, "dur": 41, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123329, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123330, "dur": 43, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123377, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123425, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123428, "dur": 43, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123473, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123475, "dur": 44, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123521, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123523, "dur": 43, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123568, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123570, "dur": 41, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123616, "dur": 49, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123668, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123670, "dur": 231, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123907, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123963, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998123965, "dur": 42, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124010, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124012, "dur": 48, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124062, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124064, "dur": 41, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124108, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124110, "dur": 40, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124153, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124155, "dur": 43, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124202, "dur": 45, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124250, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124253, "dur": 44, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124300, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124302, "dur": 44, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124349, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124351, "dur": 45, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124398, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124400, "dur": 42, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124445, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124447, "dur": 43, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124494, "dur": 42, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124539, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124541, "dur": 47, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124592, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124594, "dur": 46, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124643, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124645, "dur": 109, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124758, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124761, "dur": 57, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124823, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124827, "dur": 61, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124892, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124895, "dur": 63, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124962, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998124965, "dur": 56, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125024, "dur": 2, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125029, "dur": 56, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125089, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125092, "dur": 59, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125157, "dur": 2, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125160, "dur": 64, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125228, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125230, "dur": 55, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125287, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125290, "dur": 45, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125338, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125339, "dur": 45, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125387, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125389, "dur": 52, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125444, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125446, "dur": 79, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125529, "dur": 3, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125534, "dur": 69, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125608, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125612, "dur": 70, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125686, "dur": 2, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125689, "dur": 52, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125744, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125746, "dur": 44, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125793, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125795, "dur": 47, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125845, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125847, "dur": 37, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125887, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125889, "dur": 47, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125939, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125942, "dur": 44, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125989, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998125992, "dur": 43, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126041, "dur": 47, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126091, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126093, "dur": 45, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126140, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126142, "dur": 43, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126188, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126192, "dur": 44, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126238, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126240, "dur": 38, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126281, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126283, "dur": 43, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126329, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126331, "dur": 43, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126377, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126378, "dur": 49, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126430, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126432, "dur": 47, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126481, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126484, "dur": 41, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126527, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126529, "dur": 40, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126571, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126573, "dur": 42, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126618, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126620, "dur": 66, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126691, "dur": 2, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126695, "dur": 65, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126765, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126769, "dur": 57, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126829, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126831, "dur": 35, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126870, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126872, "dur": 55, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126930, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126932, "dur": 56, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126993, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998126996, "dur": 58, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127058, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127061, "dur": 72, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127138, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127141, "dur": 67, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127212, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127215, "dur": 47, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127265, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127267, "dur": 45, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127314, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127316, "dur": 45, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127364, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127366, "dur": 43, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127414, "dur": 43, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127459, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127461, "dur": 42, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127508, "dur": 34, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127548, "dur": 41, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127591, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127593, "dur": 42, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127638, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127640, "dur": 42, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127685, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127687, "dur": 45, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127735, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127737, "dur": 43, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127785, "dur": 44, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127832, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127834, "dur": 38, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127875, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127877, "dur": 42, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127923, "dur": 43, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127969, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998127970, "dur": 44, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128017, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128019, "dur": 45, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128066, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128068, "dur": 48, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128119, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128121, "dur": 43, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128166, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128168, "dur": 43, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128216, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128260, "dur": 41, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128304, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128307, "dur": 45, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128354, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128356, "dur": 44, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128402, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128404, "dur": 43, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128450, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128452, "dur": 44, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128497, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128499, "dur": 38, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128540, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128542, "dur": 41, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128585, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128587, "dur": 40, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128632, "dur": 47, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128682, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128684, "dur": 41, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128727, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128729, "dur": 45, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128777, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128779, "dur": 37, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128818, "dur": 1, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128821, "dur": 39, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128862, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128864, "dur": 46, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128913, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128915, "dur": 44, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128961, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998128964, "dur": 44, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129011, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129014, "dur": 45, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129062, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129063, "dur": 36, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129102, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129104, "dur": 45, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129151, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129153, "dur": 43, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129199, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129200, "dur": 46, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129249, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129252, "dur": 45, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129299, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129301, "dur": 46, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129349, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129352, "dur": 40, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129395, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129397, "dur": 39, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129438, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129440, "dur": 45, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129488, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129489, "dur": 42, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129534, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129536, "dur": 43, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129583, "dur": 44, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129629, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129631, "dur": 44, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129678, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129680, "dur": 38, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129721, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129723, "dur": 43, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129769, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129771, "dur": 44, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129817, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129818, "dur": 42, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129863, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129865, "dur": 43, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129911, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129912, "dur": 43, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129958, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998129961, "dur": 42, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130005, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130007, "dur": 35, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130045, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130047, "dur": 38, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130088, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130090, "dur": 47, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130140, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130142, "dur": 42, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130187, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130190, "dur": 184, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130378, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130380, "dur": 68, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130450, "dur": 2, "ph": "X", "name": "ProcessMessages 2030", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130454, "dur": 44, "ph": "X", "name": "ReadAsync 2030", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130501, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130502, "dur": 36, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130541, "dur": 1, "ph": "X", "name": "ProcessMessages 97", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130543, "dur": 38, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130586, "dur": 35, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130625, "dur": 48, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130677, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130679, "dur": 52, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130734, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130737, "dur": 56, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130796, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130798, "dur": 46, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130847, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130849, "dur": 35, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130888, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130933, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130935, "dur": 43, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130980, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998130982, "dur": 44, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131028, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131030, "dur": 42, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131075, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131076, "dur": 39, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131118, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131130, "dur": 43, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131176, "dur": 201, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131379, "dur": 82, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131465, "dur": 3, "ph": "X", "name": "ProcessMessages 2977", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131469, "dur": 47, "ph": "X", "name": "ReadAsync 2977", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131518, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131520, "dur": 42, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131565, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131566, "dur": 47, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131616, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131618, "dur": 37, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131657, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131660, "dur": 42, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131705, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131706, "dur": 46, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131755, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131757, "dur": 42, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131802, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131803, "dur": 42, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131848, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131850, "dur": 42, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131894, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131896, "dur": 43, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131942, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131944, "dur": 35, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998131984, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132030, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132033, "dur": 43, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132079, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132081, "dur": 43, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132126, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132129, "dur": 43, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132175, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132177, "dur": 42, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132222, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132225, "dur": 36, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132263, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132266, "dur": 39, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132309, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132352, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132354, "dur": 44, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132400, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132402, "dur": 44, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132448, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132450, "dur": 44, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132497, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132498, "dur": 43, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132544, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132546, "dur": 45, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132594, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132596, "dur": 43, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132642, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132644, "dur": 37, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132685, "dur": 47, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132735, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132737, "dur": 54, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132795, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132797, "dur": 48, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132848, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132849, "dur": 39, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132891, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132893, "dur": 43, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132939, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132941, "dur": 40, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132984, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998132986, "dur": 105, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133094, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133097, "dur": 69, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133171, "dur": 2, "ph": "X", "name": "ProcessMessages 1408", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133175, "dur": 56, "ph": "X", "name": "ReadAsync 1408", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133235, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133238, "dur": 55, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133297, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133300, "dur": 60, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133363, "dur": 2, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133367, "dur": 50, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133420, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133423, "dur": 62, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133490, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133494, "dur": 63, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133560, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133562, "dur": 90, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133657, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133660, "dur": 78, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133743, "dur": 2, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133747, "dur": 65, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133816, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133818, "dur": 56, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133876, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133878, "dur": 47, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133928, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133930, "dur": 52, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133984, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998133986, "dur": 52, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134041, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134043, "dur": 45, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134091, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134093, "dur": 54, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134149, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134151, "dur": 53, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134206, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134208, "dur": 62, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134273, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134275, "dur": 47, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134326, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134329, "dur": 51, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134383, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134385, "dur": 49, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134437, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134439, "dur": 54, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134495, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134497, "dur": 55, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134554, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134556, "dur": 44, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134603, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134604, "dur": 56, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134663, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134665, "dur": 51, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134719, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134720, "dur": 64, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134787, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134789, "dur": 48, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134839, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134841, "dur": 52, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134895, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134897, "dur": 47, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134947, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998134949, "dur": 51, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135003, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135005, "dur": 51, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135059, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135061, "dur": 46, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135111, "dur": 52, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135166, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135168, "dur": 53, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135223, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135225, "dur": 52, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135279, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135281, "dur": 48, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135332, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135334, "dur": 50, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135388, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135390, "dur": 49, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135442, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135443, "dur": 54, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135501, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135503, "dur": 52, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135558, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135561, "dur": 54, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135618, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135620, "dur": 53, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135675, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135677, "dur": 52, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135732, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135735, "dur": 54, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135792, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135794, "dur": 62, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135860, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135864, "dur": 60, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135927, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135929, "dur": 48, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135980, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998135982, "dur": 49, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136033, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136035, "dur": 44, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136083, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136085, "dur": 53, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136140, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136143, "dur": 41, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136186, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136188, "dur": 44, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136235, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136237, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136288, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136342, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136345, "dur": 51, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136399, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136401, "dur": 39, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136442, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136445, "dur": 96, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136545, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136600, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136602, "dur": 52, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136656, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136658, "dur": 41, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136702, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136704, "dur": 79, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136787, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136849, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136850, "dur": 52, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136905, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136907, "dur": 44, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136953, "dur": 2, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998136957, "dur": 87, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137048, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137103, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137105, "dur": 54, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137161, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137163, "dur": 40, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137206, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137208, "dur": 76, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137288, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137344, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137346, "dur": 53, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137402, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137404, "dur": 40, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137448, "dur": 81, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137532, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137586, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137588, "dur": 52, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137642, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137645, "dur": 41, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137688, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137690, "dur": 76, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137769, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137821, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137824, "dur": 69, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137898, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137902, "dur": 49, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137954, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998137957, "dur": 89, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138052, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138129, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138132, "dur": 51, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138186, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138188, "dur": 83, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138275, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138329, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138332, "dur": 44, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138378, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138380, "dur": 37, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138420, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138423, "dur": 109, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138537, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138585, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138587, "dur": 45, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138635, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138637, "dur": 34, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138674, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138676, "dur": 94, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138774, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138825, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138826, "dur": 51, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138881, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138883, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138935, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998138937, "dur": 90, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139032, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139081, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139083, "dur": 40, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139128, "dur": 42, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139172, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139173, "dur": 35, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139213, "dur": 97, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139314, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139364, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139367, "dur": 45, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139415, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139417, "dur": 44, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139463, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139465, "dur": 36, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139503, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139505, "dur": 96, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139605, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139653, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139656, "dur": 42, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139701, "dur": 42, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139746, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139748, "dur": 37, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139789, "dur": 92, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139884, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139929, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139931, "dur": 43, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139976, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998139978, "dur": 43, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140025, "dur": 37, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140064, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140065, "dur": 98, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140168, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140215, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140218, "dur": 41, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140262, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140263, "dur": 42, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140310, "dur": 35, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140348, "dur": 95, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140447, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140495, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140497, "dur": 42, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140541, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140543, "dur": 41, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140587, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140589, "dur": 37, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140628, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140630, "dur": 95, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140729, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140777, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140780, "dur": 42, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140826, "dur": 42, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140871, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140873, "dur": 35, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140911, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998140912, "dur": 93, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141009, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141057, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141059, "dur": 34, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141097, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141148, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141150, "dur": 35, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141187, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141189, "dur": 88, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141281, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141331, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141333, "dur": 111, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141449, "dur": 2, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141454, "dur": 70, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141527, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141530, "dur": 54, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141589, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141592, "dur": 48, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141643, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141646, "dur": 99, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141752, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141827, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141829, "dur": 71, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141904, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141907, "dur": 70, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141982, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998141986, "dur": 77, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142067, "dur": 2, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142071, "dur": 64, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142138, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142140, "dur": 53, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142198, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142202, "dur": 51, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142257, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142260, "dur": 86, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142350, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142352, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142415, "dur": 2, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142418, "dur": 57, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142480, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142483, "dur": 89, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142578, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142642, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142645, "dur": 54, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142703, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142706, "dur": 92, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142804, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142871, "dur": 2, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142874, "dur": 56, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142934, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998142937, "dur": 92, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143034, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143095, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143098, "dur": 58, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143160, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143163, "dur": 57, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143225, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143228, "dur": 58, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143289, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143292, "dur": 49, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143345, "dur": 3, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143349, "dur": 51, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143403, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143406, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143486, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143550, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143553, "dur": 55, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143612, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143615, "dur": 141, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143762, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143825, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143828, "dur": 56, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143887, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143890, "dur": 47, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143941, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998143943, "dur": 81, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144028, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144031, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144093, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144096, "dur": 59, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144159, "dur": 2, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144163, "dur": 59, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144225, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144228, "dur": 58, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144290, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144293, "dur": 50, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144347, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144350, "dur": 50, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144404, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144407, "dur": 75, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144485, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144488, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144549, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144552, "dur": 53, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144609, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144611, "dur": 49, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144664, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144668, "dur": 75, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144748, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144750, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144812, "dur": 2, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144815, "dur": 55, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144874, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144877, "dur": 86, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998144968, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145030, "dur": 2, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145034, "dur": 55, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145092, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145095, "dur": 90, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145191, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145252, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145255, "dur": 55, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145314, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145317, "dur": 101, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145424, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145485, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145488, "dur": 55, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145546, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145550, "dur": 54, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145607, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145610, "dur": 89, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145703, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145706, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145765, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145768, "dur": 55, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145827, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145831, "dur": 98, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145933, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145935, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998145997, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146000, "dur": 53, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146058, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146062, "dur": 90, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146158, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146220, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146223, "dur": 58, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146284, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146288, "dur": 99, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146390, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146394, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146451, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146454, "dur": 54, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146512, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146515, "dur": 82, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146603, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146664, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146667, "dur": 54, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146724, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146727, "dur": 94, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146827, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146888, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146891, "dur": 55, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146949, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998146952, "dur": 94, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147053, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147115, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147118, "dur": 56, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147177, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147180, "dur": 102, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147286, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147288, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147349, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147352, "dur": 55, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147411, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147414, "dur": 49, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147467, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147469, "dur": 108, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147584, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147665, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147668, "dur": 61, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147733, "dur": 2, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147737, "dur": 117, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147858, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147861, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147930, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998147934, "dur": 72, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148010, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148014, "dur": 49, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148066, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148069, "dur": 96, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148169, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148172, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148250, "dur": 2, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148254, "dur": 67, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148325, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148328, "dur": 112, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148444, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148446, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148520, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148523, "dur": 65, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148590, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148593, "dur": 56, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148653, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148656, "dur": 95, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148756, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148824, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148827, "dur": 64, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148894, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148897, "dur": 29, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148928, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998148931, "dur": 118, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149054, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149127, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149133, "dur": 63, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149199, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149202, "dur": 112, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149318, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149320, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149391, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149394, "dur": 65, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149462, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149466, "dur": 132, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149603, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149675, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149677, "dur": 71, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149752, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149755, "dur": 56, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149813, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149815, "dur": 123, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149943, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998149946, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150021, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150025, "dur": 61, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150089, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150092, "dur": 53, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150150, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150152, "dur": 142, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150299, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150302, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150362, "dur": 2, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150366, "dur": 65, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150435, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150438, "dur": 60, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150501, "dur": 16, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150520, "dur": 78, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150602, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150607, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150688, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150691, "dur": 60, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150755, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150759, "dur": 64, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150826, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150828, "dur": 95, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150927, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998150930, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151005, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151008, "dur": 61, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151073, "dur": 2, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151077, "dur": 120, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151202, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151272, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151276, "dur": 70, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151349, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151353, "dur": 108, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151468, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151543, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151546, "dur": 63, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151613, "dur": 2, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151616, "dur": 130, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151750, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151752, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151823, "dur": 2, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151826, "dur": 65, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151896, "dur": 3, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151900, "dur": 56, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151960, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998151964, "dur": 104, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152075, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152145, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152149, "dur": 60, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152213, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152216, "dur": 58, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152278, "dur": 2, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152281, "dur": 59, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152344, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152347, "dur": 65, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152418, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152422, "dur": 54, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152480, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152483, "dur": 62, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152552, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152603, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152606, "dur": 132, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152741, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152744, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152840, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152844, "dur": 73, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152921, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152923, "dur": 63, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152989, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998152991, "dur": 65, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153059, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153062, "dur": 48, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153113, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153115, "dur": 53, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153172, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153175, "dur": 169, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153348, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153350, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153422, "dur": 2, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153425, "dur": 56, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153484, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153486, "dur": 56, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153546, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153548, "dur": 50, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153601, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153603, "dur": 56, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153662, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153666, "dur": 56, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153727, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153731, "dur": 178, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153913, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153916, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153972, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998153975, "dur": 42, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154021, "dur": 486, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154511, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154598, "dur": 9, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154610, "dur": 49, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154663, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154667, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154713, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154718, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154763, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154766, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154808, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154811, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154855, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154857, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154906, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154909, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154971, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998154974, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155024, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155027, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155073, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155076, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155119, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155122, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155165, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155168, "dur": 48, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155220, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155222, "dur": 50, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155277, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155279, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155335, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155339, "dur": 43, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155385, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155388, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155439, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155443, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155488, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155490, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155531, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155533, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155576, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155579, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155625, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155628, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155677, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155680, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155732, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155737, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155781, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155784, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155826, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155829, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155870, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155873, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155913, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155916, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155957, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998155960, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156005, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156009, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156050, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156052, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156096, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156100, "dur": 42, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156145, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156148, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156193, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156196, "dur": 39, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156241, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156283, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156286, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156338, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156342, "dur": 43, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156388, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156391, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156434, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156438, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156481, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156483, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156534, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156538, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156586, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156589, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156635, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156637, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156680, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156683, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156726, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156728, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156823, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156825, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156868, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156871, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156934, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156936, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156980, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998156985, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157036, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157040, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157086, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157090, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157139, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157141, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157188, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157191, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157242, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157246, "dur": 40, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157290, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157293, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157336, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157339, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157376, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157378, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157419, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157425, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157478, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157482, "dur": 47, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157533, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157537, "dur": 47, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157589, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157592, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157640, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157643, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157688, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157692, "dur": 45, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157740, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157744, "dur": 47, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157798, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157842, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157845, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157898, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157902, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157948, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157952, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157996, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998157999, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158048, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158051, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158095, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158098, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158143, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158147, "dur": 61, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158212, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158215, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158270, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158274, "dur": 60, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158338, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158341, "dur": 47, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158392, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158396, "dur": 63, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158464, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158467, "dur": 291, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158767, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158819, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158822, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158873, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158875, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158977, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998158980, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159036, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159040, "dur": 49, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159094, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159098, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159149, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159153, "dur": 44, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159201, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159204, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159254, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159256, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159302, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159305, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159350, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159353, "dur": 39, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159396, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159399, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159449, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159453, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159499, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159503, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159549, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159552, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159600, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159603, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159655, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159658, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159707, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159712, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159759, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159761, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159827, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159830, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159886, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159889, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159942, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159945, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159990, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998159993, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160036, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160039, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160085, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160088, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160137, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160141, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160186, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160190, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160236, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160241, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160287, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160291, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160335, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160338, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160383, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160386, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160433, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160436, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160481, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160483, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160529, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160533, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160580, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160583, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160631, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160634, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160689, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160691, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160853, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160856, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160900, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160902, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160983, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998160986, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998161034, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998161037, "dur": 755, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998161797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998161799, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998161848, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998161851, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998161898, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998161901, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998162067, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998162070, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998162113, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998162116, "dur": 7777, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998169902, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998169907, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998169940, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998169943, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998169994, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998169997, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998170050, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998170053, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998170156, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998170159, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998170202, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998170205, "dur": 1171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171380, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171384, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171444, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171448, "dur": 250, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171702, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171705, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171749, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171752, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171797, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171800, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171912, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171914, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171957, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171960, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998171999, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172002, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172039, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172041, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172156, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172158, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172192, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172194, "dur": 511, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172709, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172713, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172760, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172763, "dur": 165, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172933, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172935, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172982, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998172985, "dur": 201, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173190, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173192, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173252, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173255, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173409, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173467, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173470, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173513, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173515, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173635, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173638, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173682, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173685, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173757, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173760, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173804, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173806, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173853, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173856, "dur": 41, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173901, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173904, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173947, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998173949, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174010, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174050, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174052, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174102, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174105, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174143, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174145, "dur": 85, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174236, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174274, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174277, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174331, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174334, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174602, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174646, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174649, "dur": 220, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174873, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174875, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174926, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174930, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174971, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998174974, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175023, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175026, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175071, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175074, "dur": 137, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175215, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175218, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175267, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175269, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175417, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175420, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175474, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175477, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175527, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175530, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175579, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175581, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175628, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175631, "dur": 189, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175823, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175826, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175866, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998175868, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176013, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176015, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176062, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176066, "dur": 142, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176214, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176217, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176259, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176262, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176288, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176325, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176328, "dur": 216, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176550, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176590, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176593, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176636, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176639, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176692, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176728, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176730, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176836, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176885, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176887, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176929, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176931, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176971, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998176974, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177155, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177157, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177201, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177203, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177242, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177244, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177299, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177336, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177338, "dur": 209, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177551, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177554, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177594, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177597, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177641, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177644, "dur": 227, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177875, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177877, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177917, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998177920, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178076, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178078, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178126, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178129, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178406, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178449, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178452, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178584, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178586, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178628, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178631, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178824, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178864, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998178866, "dur": 225, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179094, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179097, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179137, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179139, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179297, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179299, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179335, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179338, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179379, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179382, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179421, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179423, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179491, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179493, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998179528, "dur": 821, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180354, "dur": 58, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180417, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180423, "dur": 43, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180470, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180472, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180514, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180517, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180813, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180816, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180858, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180861, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180921, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998180923, "dur": 471, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181399, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181401, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181447, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181454, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181597, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181600, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181646, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181650, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181691, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181694, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181861, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181864, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181913, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181916, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181967, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998181970, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182013, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182016, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182213, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182216, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182260, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182263, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182308, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182311, "dur": 195, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182513, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182560, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182563, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182625, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182627, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182673, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998182676, "dur": 465, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183146, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183149, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183215, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183219, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183272, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183275, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183449, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183509, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183512, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183555, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183557, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183599, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183602, "dur": 249, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183859, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183900, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998183903, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184011, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184013, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184055, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184057, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184109, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184112, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184150, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184153, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184255, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184293, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184296, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184351, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184388, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184391, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184511, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184555, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184557, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184594, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184597, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184666, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184703, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184705, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184756, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184759, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184798, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184800, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184948, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184992, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998184994, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185091, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185094, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185137, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185140, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185210, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185212, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185261, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185264, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185313, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185316, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185441, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185444, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185488, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185491, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185567, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185570, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185616, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185621, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185798, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185849, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185851, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185954, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998185956, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186002, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186005, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186045, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186048, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186218, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186220, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186261, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186267, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186310, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186313, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186371, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186374, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186480, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186482, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186523, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186525, "dur": 191, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186720, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186723, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186763, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186767, "dur": 109, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186882, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186922, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186925, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186966, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998186969, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187012, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187015, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187054, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187057, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187185, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187226, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187229, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187276, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187279, "dur": 211, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187496, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187543, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187545, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187589, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187591, "dur": 274, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187873, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187919, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187922, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187971, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998187973, "dur": 220, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188198, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188200, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188250, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188253, "dur": 242, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188500, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188503, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188554, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188558, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188621, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188623, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188656, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188659, "dur": 294, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188958, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998188960, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189026, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189029, "dur": 201, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189237, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189293, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189295, "dur": 174, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189474, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189477, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189533, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189537, "dur": 232, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189774, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189776, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189832, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189835, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189892, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998189894, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190046, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190048, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190103, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190106, "dur": 365, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190476, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190571, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190573, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190626, "dur": 265, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190895, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190896, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190941, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998190943, "dur": 1393, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998192342, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998192345, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998192407, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998192412, "dur": 114088, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998306510, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998306515, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998306578, "dur": 1865, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998308450, "dur": 4063, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312522, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312527, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312583, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312589, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312638, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312640, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312680, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312682, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312779, "dur": 23, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312803, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312842, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998312845, "dur": 869, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998313719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998313722, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998313763, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998313766, "dur": 1211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998314983, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998314986, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315025, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315029, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315202, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315204, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315247, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315249, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315376, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315378, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315413, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315415, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315497, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315543, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315546, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315620, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315622, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315667, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998315670, "dur": 821, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998316495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998316498, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998316557, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998316560, "dur": 906, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998317471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998317475, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998317525, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998317528, "dur": 683, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998318226, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998318229, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998318280, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998318282, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998318410, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998318412, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998318453, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998318456, "dur": 698, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998319159, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998319162, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998319208, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998319211, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998319314, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998319317, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998319357, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998319360, "dur": 638, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998320001, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998320003, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998320042, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998320045, "dur": 376, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998320426, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998320428, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998320471, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998320474, "dur": 929, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998321409, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998321412, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998321456, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998321458, "dur": 572, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998322035, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998322037, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998322081, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998322084, "dur": 389, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998322478, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998322480, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998322522, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998322525, "dur": 812, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998323352, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998323356, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998323399, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998323402, "dur": 2546, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998325958, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998325963, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998326003, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998326005, "dur": 572, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998326583, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998326586, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998326636, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998326639, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998326852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998326855, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998326893, "dur": 351, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998327248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998327250, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998327286, "dur": 995, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328289, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328325, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328328, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328486, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328523, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328526, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328621, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328657, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998328659, "dur": 706, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998329369, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998329372, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998329407, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998329594, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998329597, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998329632, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998329635, "dur": 531, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998330170, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998330172, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998330224, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998330226, "dur": 184, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998330414, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998330416, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998330455, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998330457, "dur": 580, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998331042, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998331079, "dur": 855, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998331938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998331941, "dur": 737, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998332682, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998332685, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998332838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998332841, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998332881, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998332883, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998332936, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998332939, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998333088, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998333091, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998333136, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998333139, "dur": 492, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998333636, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998333638, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998333706, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998333709, "dur": 2211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998335928, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998335931, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998335971, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998335973, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336031, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336034, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336080, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336146, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336193, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336234, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336276, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998336279, "dur": 2976, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339265, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339270, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339334, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339338, "dur": 42, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339384, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339387, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339432, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339435, "dur": 35, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339472, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339474, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339513, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339515, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339551, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339554, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339594, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339597, "dur": 44, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339647, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339652, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339706, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339709, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339762, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339766, "dur": 48, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339818, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339821, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339873, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339877, "dur": 39, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339921, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339924, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339970, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998339973, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340021, "dur": 2, "ph": "X", "name": "ProcessMessages 109", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340035, "dur": 47, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340086, "dur": 6, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340093, "dur": 47, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340144, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340149, "dur": 51, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340204, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340207, "dur": 49, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340261, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340265, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340313, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340317, "dur": 58, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340379, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340384, "dur": 52, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340440, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340444, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340490, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998340493, "dur": 672, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998341169, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998341172, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998341222, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751406998341224, "dur": 1685348, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000026584, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000026588, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000026655, "dur": 25, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000026683, "dur": 3362, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000030051, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000030055, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000030109, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000030111, "dur": 20831, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000050953, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000050958, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000051021, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000051026, "dur": 273, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000051307, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000051310, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000051369, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000051375, "dur": 138981, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000190366, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000190370, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000190438, "dur": 25, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000190465, "dur": 4883, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000195359, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000195363, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000195426, "dur": 30, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000195459, "dur": 8552, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000204023, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000204028, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000204112, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000204118, "dur": 1999, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000206126, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000206131, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000206198, "dur": 30, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000206231, "dur": 1577, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000207813, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000207816, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000207872, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000207877, "dur": 34609, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000242497, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000242501, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000242574, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000242580, "dur": 2623, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000245213, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000245218, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000245348, "dur": 36, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000245386, "dur": 487, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000245878, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000245882, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000245979, "dur": 446, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751407000246431, "dur": 15310, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5212, "tid": 1097, "ts": 1751407000284454, "dur": 4049, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5212, "tid": 8589934592, "ts": 1751406998073859, "dur": 166882, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751406998240744, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751406998240755, "dur": 2069, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5212, "tid": 1097, "ts": 1751407000288506, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5212, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5212, "tid": 4294967296, "ts": 1751406998044113, "dur": 2219542, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751406998050133, "dur": 11359, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751407000263986, "dur": 8200, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751407000268855, "dur": 178, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751407000272330, "dur": 24, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5212, "tid": 1097, "ts": 1751407000288515, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751406998103968, "dur": 4925, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751406998108913, "dur": 2114, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751406998111226, "dur": 119, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751406998111346, "dur": 583, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751406998112243, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998112417, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998112714, "dur": 563, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_6BC1B44B0B58CEF0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998114369, "dur": 344, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0D58F8E6C2E92DF4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998115620, "dur": 216, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_AAC2C267FBE7A1FE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998116105, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5911DF3198B3A870.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998116187, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_9A715A3879D71476.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998116592, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998116787, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998118034, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998118428, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751406998118702, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998118990, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_7D2E9826DFBFBE48.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998119209, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998119347, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998119425, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998119655, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751406998120009, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751406998120128, "dur": 134, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998120431, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998120792, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751406998121219, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751406998121501, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751406998121774, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751406998121859, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751406998122513, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998123520, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751406998124416, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998124688, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998125049, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998125550, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998126218, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751406998126583, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751406998126818, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998127716, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998129984, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751406998130984, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751406998132345, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998132697, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998133159, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998133259, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751406998137649, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998137882, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998138910, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751406998140945, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751406998141029, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998141108, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998141343, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751406998141592, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751406998147195, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998147466, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998147771, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998148362, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998148660, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998148925, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998149210, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998149270, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998149559, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998149634, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998149959, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998150539, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998150805, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998151081, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998151355, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998151681, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998152357, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998152436, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751406998153185, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751406998111983, "dur": 41454, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751406998153477, "dur": 2091420, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407000244898, "dur": 302, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407000245319, "dur": 74, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407000245433, "dur": 3557, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751406998112124, "dur": 41370, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998153526, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998154129, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_83F4E4F0FA017AFD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998154356, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_11834CDAF2387C83.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998154479, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_611E434C56DD2756.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998154636, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998154829, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_805E31AC8FEB08D0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998155168, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998155448, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_2A790542FCF7EA37.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998155897, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998156344, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998156458, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998156535, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998156758, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751406998156879, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998157332, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998157485, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998157591, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751406998157961, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751406998158342, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998158765, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998158907, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998158993, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998159290, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998159593, "dur": 127, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751406998160169, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998160407, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998160794, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998161111, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998161305, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998161579, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998161831, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998162117, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998162380, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998162703, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998162945, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998163210, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998163512, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998163759, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998164538, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998164796, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998165060, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998165306, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998165557, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998165865, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998166163, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998166418, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998166676, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998166953, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998167263, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998167520, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998167775, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998168247, "dur": 1212, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Interface\\Annotations\\AnnotationDisabler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751406998168048, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998169559, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998170305, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998170563, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998171167, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998171477, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998171540, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998172287, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998172509, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998173240, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998173501, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998173710, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998174377, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998174509, "dur": 1024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998175614, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998175887, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998176177, "dur": 1028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998177206, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998177452, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998178922, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998179622, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998179867, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998180528, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998180790, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998181523, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998181791, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998182097, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998182203, "dur": 1311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998183515, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998183588, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998183834, "dur": 1115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998184950, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998185022, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998185142, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998185370, "dur": 1046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998186514, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751406998186755, "dur": 673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998187504, "dur": 58286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998245791, "dur": 63729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998309521, "dur": 2537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998312060, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998312149, "dur": 2753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998314958, "dur": 3596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998318555, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998318736, "dur": 3493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998322231, "dur": 4185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998326434, "dur": 2514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998328949, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998329174, "dur": 2850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998332069, "dur": 596, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998332669, "dur": 2848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998335518, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998335605, "dur": 3133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751406998338740, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751406998338938, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751406998339100, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751406998339199, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751406998339722, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751406998340046, "dur": 1904829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998112253, "dur": 41292, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998153550, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998154143, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0106BA1057A1C6CC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998154306, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_13694A2D45790405.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998154445, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998154720, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998154904, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998155165, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7E2204058CA93257.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998155483, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998155662, "dur": 5649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998161408, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998161643, "dur": 7743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998169499, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998169730, "dur": 1165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998171022, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998171299, "dur": 1644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998172978, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998173089, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998173323, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998173395, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998174108, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998174201, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998174420, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998174481, "dur": 1343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998175896, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998176127, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998176321, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998176464, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998176768, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998177653, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998178398, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998179620, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998179698, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998179852, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998180223, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\XR\\Haptics\\SendHapticImpulseCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751406998180166, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998181414, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998181570, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998181793, "dur": 1840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998183725, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998183940, "dur": 1532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998185603, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751406998185828, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998185919, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998186728, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998186837, "dur": 55576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998244209, "dur": 218, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1751406998244428, "dur": 1145, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1751406998245577, "dur": 197, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1751406998242415, "dur": 3368, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998245784, "dur": 63690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998309476, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998312131, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998312215, "dur": 2856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998315072, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998315236, "dur": 2703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998318004, "dur": 2924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998321006, "dur": 7150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998328233, "dur": 2911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998331145, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998331503, "dur": 772, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998332279, "dur": 3261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998335541, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751406998335823, "dur": 3243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751406998339143, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751406998339723, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751406998339817, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751406998340019, "dur": 1688391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407000028434, "dur": 17731, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751407000028413, "dur": 20098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407000050451, "dur": 395, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751407000051587, "dur": 138327, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751407000202899, "dur": 39073, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751407000202858, "dur": 39117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751407000242006, "dur": 2753, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751406998112185, "dur": 41325, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998153808, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998154233, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_91801C7D6E952EB7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998154454, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_69C31F9D75BAAB88.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998154632, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_89B82555D6AF826E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998154689, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998154828, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_92A5F53C232CE1D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998154926, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_5BB3CA76865503EA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998155045, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_177F4FE55DE2E02C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998155362, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751406998155651, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998155803, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998156035, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751406998156300, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998156441, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998156493, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751406998156563, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751406998156865, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998156935, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751406998156991, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998157092, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751406998157214, "dur": 69, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751406998157303, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751406998157401, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998157497, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751406998157599, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751406998157708, "dur": 707, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751406998158429, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998158733, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751406998159309, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751406998160020, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751406998160241, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998160575, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998160839, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998161245, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998161600, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998161892, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998162169, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998162550, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998162800, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998163057, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998163312, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998163812, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Implementation\\GraphObject.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751406998163559, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998164315, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998164620, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998164888, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998165150, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998165410, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998165702, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998166056, "dur": 814, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Description\\IUnitDescriptor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751406998166040, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998167212, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998167482, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998167768, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998168052, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998168346, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998168623, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998168864, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998169123, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998169369, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998169716, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998170060, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998170344, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998170599, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998170858, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998171121, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998171356, "dur": 10073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998181431, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998181569, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998181792, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998182992, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998183157, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998183429, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998184089, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998184166, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998184248, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998184518, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998185563, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998185794, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998186395, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998186504, "dur": 1322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998187828, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751406998188069, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998188810, "dur": 121696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998310507, "dur": 2693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998313202, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998313292, "dur": 2723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998316130, "dur": 3801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998320003, "dur": 9582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998329587, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998329747, "dur": 2706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998332498, "dur": 3149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998335649, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998335739, "dur": 3607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998339349, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998339463, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751406998339542, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751406998340054, "dur": 1904754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998112235, "dur": 41293, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998153534, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998154456, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998154601, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_432A54C7AE63CA63.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998154733, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998154899, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_9D0087D0CAC25D2C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998155105, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_FB81041CC44652D8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998155206, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998155631, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998155916, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998156000, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998156188, "dur": 4182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998160470, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998160740, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998161086, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998161335, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998161648, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998161907, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998162176, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998162436, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998162741, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998162986, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998163247, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998163503, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998163815, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\AbstractShaderProperty.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751406998164317, "dur": 1403, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Enumerations\\Precision.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751406998163753, "dur": 2143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998165896, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998166252, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998166565, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998166821, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998167152, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998167418, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998167705, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998167959, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998168229, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998168537, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998168787, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998169044, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998169291, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998169545, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998169838, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998170126, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998170419, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998170665, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998170930, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998171246, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998171600, "dur": 2794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998174429, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998174628, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998174976, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998175035, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998176308, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998176480, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998176733, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998177128, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998178164, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998178853, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998178975, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998179271, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998179929, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998179993, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998180064, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998180381, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998180969, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998181414, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998181573, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998181778, "dur": 1022, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998182802, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998183416, "dur": 505, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998183936, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998184253, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998184338, "dur": 1919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998186339, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998186586, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998187666, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998187819, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998188088, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998188932, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998189050, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998189300, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998190002, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998190118, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751406998190434, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751406998191760, "dur": 91, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751406998192957, "dur": 1833133, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407000029067, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751407000028407, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751407000029571, "dur": 16578, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751407000029567, "dur": 18243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407000049818, "dur": 407, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751407000051326, "dur": 143586, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751407000207042, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751407000207026, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751407000207371, "dur": 37442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998112296, "dur": 41259, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998153568, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_1AA5CC13572AAA47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998153654, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_AAC2C267FBE7A1FE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998153802, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998154269, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998154409, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_F0DA661B5DCD99FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998154531, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_EA250D0B58FB5B36.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998154686, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_EA250D0B58FB5B36.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998154830, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5E3C6CF57067B635.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998155595, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998155673, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998155788, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_7C46D60DEB68F39C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998156266, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998156333, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998156410, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998156536, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751406998156946, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998157317, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998157461, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998157618, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751406998157905, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998157978, "dur": 386, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751406998158366, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998158429, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998158709, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998159018, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998159284, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998159562, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998159712, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998159816, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751406998159916, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998160144, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998160433, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998160927, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998161260, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998161580, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998161849, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998162110, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998162380, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998162715, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998162967, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998163399, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998163822, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\SerializableTextureArray.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751406998163647, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998164427, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998164683, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998164929, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998165180, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998165581, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\Camera\\CameraUI.Skin.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751406998165440, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998166285, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998166593, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998166884, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998167170, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998167423, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998167680, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998167939, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998168207, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998168489, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998168960, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998169225, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998169631, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998170039, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998170353, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998170618, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998170867, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998171139, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998171339, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998171488, "dur": 1780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998173269, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998173368, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998173643, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998173700, "dur": 1404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998175183, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998175402, "dur": 6327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998181838, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998182086, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998182652, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998182717, "dur": 1671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998184391, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998184667, "dur": 1329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998186059, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998186293, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998187076, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998187140, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751406998187440, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998188045, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998188135, "dur": 121459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998309596, "dur": 2713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998312311, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998312398, "dur": 2588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998314987, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998315081, "dur": 2648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998317780, "dur": 8995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998326831, "dur": 3107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998329987, "dur": 2550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998332594, "dur": 3083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998335678, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751406998335768, "dur": 2970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998338807, "dur": 1887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751406998340759, "dur": 1904142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998112351, "dur": 41240, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998153608, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BF2CDC1257CA045F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751406998153797, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751406998154159, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751406998154383, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_656AB5FDB0417D9D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751406998154512, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751406998154724, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751406998155155, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751406998155307, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751406998155533, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998155684, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998155881, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751406998155956, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751406998156232, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751406998156312, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998157123, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751406998157326, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751406998157656, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998158024, "dur": 351, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751406998158403, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998159198, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751406998159450, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998159558, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751406998159961, "dur": 592, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751406998160554, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998160855, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998161242, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998161661, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998162018, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998162304, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998162638, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998162901, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998163169, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998163430, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998163798, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\MinimalGraphData.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751406998163688, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998164478, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998164731, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998164991, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998165254, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998165496, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998165799, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998166086, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998166789, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998167058, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998167343, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998167611, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998167876, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998168149, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998168454, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998168704, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998168972, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998169240, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998169632, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998170032, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998170314, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998170568, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998170819, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998171119, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751406998171360, "dur": 2015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998173466, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751406998173682, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998176174, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751406998176436, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998177098, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998177189, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751406998178276, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998178371, "dur": 229, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998179090, "dur": 126986, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751406998309473, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998312071, "dur": 2432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998314561, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998317042, "dur": 2476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998319519, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998319585, "dur": 2391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998321977, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998322052, "dur": 2637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998324691, "dur": 790, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998325503, "dur": 2516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998328068, "dur": 2492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998330623, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998333123, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998333209, "dur": 2564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998335774, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998335845, "dur": 2885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998338733, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998338791, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751406998339026, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998339404, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751406998339467, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751406998339728, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751406998339945, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751406998340033, "dur": 1867002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751407000207059, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751407000207037, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751407000207376, "dur": 37454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998112404, "dur": 41411, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998154380, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_7049A087AD26B808.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998154497, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998154676, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998155151, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751406998155272, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998155348, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998155482, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751406998155783, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_B7135FF78EF23DD9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998156130, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998156275, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998156486, "dur": 313, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998156947, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751406998157149, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998157353, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998157608, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998157811, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998157959, "dur": 358, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751406998158321, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998158619, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998159014, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751406998159113, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998159202, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998159592, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998159647, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998159746, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998159837, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998160019, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998160130, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751406998160227, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998160561, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998161524, "dur": 1004, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Google.Protobuf.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751406998160986, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998162611, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998162890, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998163143, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998163397, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998163815, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\Vector1MaterialSlot.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751406998163642, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998164418, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998164663, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998164921, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998165175, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998165450, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998166149, "dur": 954, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Variables\\UnifiedVariableUnitOption.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751406998165766, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998167104, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998167390, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998167648, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998167905, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998168178, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998168481, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998168722, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998168986, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998169301, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998170200, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\GraphsExceptionUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751406998169928, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998171480, "dur": 883, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\AddDelete\\AddDeleteItemModeMix.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751406998170799, "dur": 1669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998172470, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998172757, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998172827, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998173865, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998174187, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998174794, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998175610, "dur": 1205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998176904, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998177138, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998177908, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998178005, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998178694, "dur": 1644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998180339, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998180485, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998180771, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998181415, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998181795, "dur": 1885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998183683, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998184105, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998184668, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998184846, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998185595, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998185874, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998188126, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998188238, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998188483, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998189416, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751406998189572, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998190007, "dur": 119475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998309484, "dur": 2763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998312249, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998312352, "dur": 2803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998315200, "dur": 6328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998321529, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998321612, "dur": 4489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998326177, "dur": 2732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998328983, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998332586, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998332661, "dur": 6180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751406998338924, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751406998339088, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751406998339681, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751406998340021, "dur": 1862877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751407000202926, "dur": 514, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751407000202900, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751407000203571, "dur": 2116, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751407000205694, "dur": 39126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998112463, "dur": 41372, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998154153, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_D80D577A1177CFEE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998154304, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998154452, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998154679, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5911DF3198B3A870.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998155249, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_C9E7F9647D79AD26.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998155882, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751406998156119, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751406998156220, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998156290, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751406998156405, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751406998156570, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751406998156797, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751406998157039, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998157335, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751406998157468, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751406998157707, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751406998157790, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998157963, "dur": 390, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751406998158384, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998158685, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751406998158847, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751406998159393, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998159710, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998160131, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998160432, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998160670, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998161097, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998161466, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998161827, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998162113, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998162392, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998162688, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998162945, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998163212, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998163465, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998163812, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\GradientShaderProperty.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751406998163713, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998164502, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998164760, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998165020, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998165273, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998165530, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998165816, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998166083, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998166404, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998166654, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998167083, "dur": 1231, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Rendering\\OnBecameInvisible.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751406998166926, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998168408, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998168675, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998168935, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998169199, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998169623, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998169934, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998170273, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998170562, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998170828, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998171376, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998171740, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998172331, "dur": 687, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998173019, "dur": 473, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998173514, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998173591, "dur": 1324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998174917, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998175043, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998175790, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998176275, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998176544, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998176810, "dur": 1354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998178165, "dur": 2997, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998181180, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998181430, "dur": 1357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998182788, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998183171, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998183607, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998184213, "dur": 561, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998184842, "dur": 1732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998186574, "dur": 3574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998190150, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751406998190478, "dur": 119000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998309480, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998311853, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998312130, "dur": 2604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998314782, "dur": 4022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998318805, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998318895, "dur": 3952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998322911, "dur": 4892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998327864, "dur": 3450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998331316, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998331509, "dur": 907, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998332420, "dur": 3003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998335485, "dur": 3250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998338786, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751406998339041, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751406998339270, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751406998339463, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751406998339816, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751406998339942, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751406998340017, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751406998340775, "dur": 1904057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751407000256901, "dur": 3145, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5212, "tid": 1097, "ts": 1751407000289169, "dur": 7075, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5212, "tid": 1097, "ts": 1751407000296299, "dur": 2695, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5212, "tid": 1097, "ts": 1751407000281434, "dur": 19257, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}