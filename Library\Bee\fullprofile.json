{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5212, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5212, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5212, "tid": 101, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5212, "tid": 101, "ts": 1751401086641045, "dur": 1313, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5212, "tid": 101, "ts": 1751401086648946, "dur": 1632, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5212, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5212, "tid": 1, "ts": 1751401085958107, "dur": 10252, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751401085968365, "dur": 107313, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751401086075694, "dur": 67763, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5212, "tid": 101, "ts": 1751401086650586, "dur": 20, "ph": "X", "name": "", "args": {}}, {"pid": 5212, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085955570, "dur": 11334, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085966907, "dur": 658517, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085968050, "dur": 4900, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085972978, "dur": 2344, "ph": "X", "name": "ProcessMessages 10985", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085975326, "dur": 382, "ph": "X", "name": "ReadAsync 10985", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085975713, "dur": 20, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085975735, "dur": 568, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976307, "dur": 2, "ph": "X", "name": "ProcessMessages 1236", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976310, "dur": 181, "ph": "X", "name": "ReadAsync 1236", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976494, "dur": 6, "ph": "X", "name": "ProcessMessages 5753", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976501, "dur": 66, "ph": "X", "name": "ReadAsync 5753", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976569, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976572, "dur": 80, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976655, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976657, "dur": 95, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976758, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976891, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976894, "dur": 60, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976957, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085976960, "dur": 49, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977012, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977014, "dur": 54, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977071, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977073, "dur": 49, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977124, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977126, "dur": 37, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977168, "dur": 141, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977313, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977315, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977378, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977380, "dur": 52, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977437, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977440, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977486, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977489, "dur": 50, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977542, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977546, "dur": 55, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977604, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977607, "dur": 123, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977734, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977736, "dur": 98, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977838, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977841, "dur": 43, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977887, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977889, "dur": 50, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977942, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977944, "dur": 52, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085977999, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978001, "dur": 66, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978070, "dur": 3, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978074, "dur": 36, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978113, "dur": 40, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978156, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978157, "dur": 51, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978211, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978213, "dur": 44, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978261, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978263, "dur": 48, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978313, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978315, "dur": 49, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978367, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978370, "dur": 56, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978428, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978430, "dur": 46, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978479, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978480, "dur": 38, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978521, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978523, "dur": 49, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978575, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978577, "dur": 143, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978723, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978726, "dur": 88, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978818, "dur": 2, "ph": "X", "name": "ProcessMessages 1213", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978822, "dur": 63, "ph": "X", "name": "ReadAsync 1213", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978888, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085978890, "dur": 124, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979020, "dur": 3, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979024, "dur": 72, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979101, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979105, "dur": 64, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979173, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979177, "dur": 75, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979257, "dur": 2, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979261, "dur": 61, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979326, "dur": 2, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979331, "dur": 67, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979403, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979407, "dur": 70, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979483, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979487, "dur": 63, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979554, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979557, "dur": 59, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979631, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979634, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979708, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979712, "dur": 66, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979781, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979784, "dur": 86, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979873, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979877, "dur": 78, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979958, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085979961, "dur": 55, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980019, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980022, "dur": 56, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980082, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980085, "dur": 57, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980145, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980148, "dur": 50, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980202, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980205, "dur": 45, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980253, "dur": 1, "ph": "X", "name": "ProcessMessages 122", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980256, "dur": 56, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980315, "dur": 2, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980319, "dur": 56, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980378, "dur": 2, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980382, "dur": 55, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980440, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980444, "dur": 45, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980492, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980495, "dur": 57, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980557, "dur": 2, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980560, "dur": 55, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980619, "dur": 11, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980632, "dur": 54, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980689, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980692, "dur": 44, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980739, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980742, "dur": 56, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980801, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980803, "dur": 55, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980862, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980865, "dur": 57, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980926, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980929, "dur": 48, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980980, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085980983, "dur": 54, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981041, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981043, "dur": 55, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981101, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981104, "dur": 54, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981162, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981165, "dur": 64, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981232, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981235, "dur": 51, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981290, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981294, "dur": 55, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981353, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981356, "dur": 57, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981416, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981419, "dur": 55, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981476, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981478, "dur": 27, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981509, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981555, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981557, "dur": 43, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981603, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981605, "dur": 52, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981659, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981661, "dur": 43, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981706, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981708, "dur": 38, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981749, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981751, "dur": 79, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981834, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981881, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981882, "dur": 42, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981926, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981928, "dur": 42, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981973, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085981975, "dur": 43, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982020, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982022, "dur": 37, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982061, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982063, "dur": 41, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982108, "dur": 41, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982151, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982154, "dur": 43, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982200, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982202, "dur": 44, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982248, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982249, "dur": 43, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982297, "dur": 36, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982336, "dur": 37, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982377, "dur": 42, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982421, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982461, "dur": 55, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982518, "dur": 1, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982520, "dur": 43, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982566, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982567, "dur": 33, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982604, "dur": 235, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982843, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982889, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982892, "dur": 44, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982938, "dur": 7, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982947, "dur": 45, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982995, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085982997, "dur": 28, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983026, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983029, "dur": 65, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983097, "dur": 38, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983139, "dur": 42, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983183, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983185, "dur": 43, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983230, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983232, "dur": 43, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983277, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983279, "dur": 41, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983322, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983324, "dur": 37, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983363, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983365, "dur": 41, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983410, "dur": 45, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983459, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983461, "dur": 56, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983521, "dur": 2, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983524, "dur": 74, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983602, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983607, "dur": 56, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983666, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983669, "dur": 61, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983733, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983736, "dur": 59, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983797, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983800, "dur": 58, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983862, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983865, "dur": 51, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983920, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085983922, "dur": 124, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984049, "dur": 1, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984051, "dur": 60, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984114, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984117, "dur": 54, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984173, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984176, "dur": 53, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984231, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984233, "dur": 60, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984296, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984299, "dur": 57, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984359, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984361, "dur": 58, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984422, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984424, "dur": 52, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984479, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984481, "dur": 59, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984543, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984545, "dur": 57, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984606, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984608, "dur": 54, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984665, "dur": 10, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984676, "dur": 52, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984732, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984734, "dur": 59, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984796, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984799, "dur": 60, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984862, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984864, "dur": 59, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984926, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984929, "dur": 52, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984983, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085984986, "dur": 57, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985046, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985048, "dur": 55, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985107, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985109, "dur": 57, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985169, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985172, "dur": 51, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985225, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985227, "dur": 58, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985288, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985291, "dur": 58, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985352, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985354, "dur": 60, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985417, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985419, "dur": 48, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985471, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985473, "dur": 60, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985536, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985538, "dur": 56, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985597, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985599, "dur": 57, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985659, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985661, "dur": 51, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985716, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985718, "dur": 57, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985778, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985781, "dur": 58, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985842, "dur": 2, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985845, "dur": 58, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985906, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985908, "dur": 49, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985960, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085985962, "dur": 57, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986022, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986024, "dur": 59, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986087, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986089, "dur": 56, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986147, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986151, "dur": 49, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986203, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986205, "dur": 59, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986267, "dur": 1, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986269, "dur": 59, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986331, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986333, "dur": 58, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986394, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986397, "dur": 52, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986451, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986453, "dur": 54, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986510, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986512, "dur": 59, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986574, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986577, "dur": 58, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986637, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986639, "dur": 53, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986695, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986697, "dur": 57, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986757, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986759, "dur": 59, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986822, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986824, "dur": 57, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986884, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986886, "dur": 63, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986952, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085986955, "dur": 53, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987011, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987014, "dur": 98, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987117, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987120, "dur": 74, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987198, "dur": 2, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987201, "dur": 100, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987306, "dur": 2, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987309, "dur": 62, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987375, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987378, "dur": 74, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987458, "dur": 2, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987462, "dur": 79, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987546, "dur": 3, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987552, "dur": 69, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987624, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987627, "dur": 62, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987692, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987695, "dur": 68, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987767, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987770, "dur": 68, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987843, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987848, "dur": 75, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987928, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085987931, "dur": 76, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988012, "dur": 2, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988016, "dur": 71, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988092, "dur": 2, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988096, "dur": 86, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988185, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988188, "dur": 81, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988273, "dur": 2, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988276, "dur": 53, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988333, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988335, "dur": 51, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988389, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988392, "dur": 49, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988444, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988447, "dur": 51, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988502, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988505, "dur": 68, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988577, "dur": 2, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988581, "dur": 57, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988641, "dur": 2, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988644, "dur": 55, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988702, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988705, "dur": 53, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988762, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988764, "dur": 56, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988824, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988827, "dur": 57, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988887, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988890, "dur": 51, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988945, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085988948, "dur": 53, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989007, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989070, "dur": 2, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989073, "dur": 57, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989133, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989137, "dur": 56, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989196, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989199, "dur": 49, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989251, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989254, "dur": 58, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989315, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989318, "dur": 55, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989377, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989380, "dur": 52, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989435, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989462, "dur": 52, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989518, "dur": 260, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989781, "dur": 103, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989886, "dur": 4, "ph": "X", "name": "ProcessMessages 3886", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989891, "dur": 39, "ph": "X", "name": "ReadAsync 3886", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989932, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989934, "dur": 41, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989977, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085989979, "dur": 44, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990025, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990026, "dur": 43, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990072, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990074, "dur": 42, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990118, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990120, "dur": 43, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990165, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990166, "dur": 35, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990205, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990251, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990253, "dur": 41, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990297, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990299, "dur": 44, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990345, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990347, "dur": 40, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990389, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990391, "dur": 54, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990451, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990500, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990502, "dur": 45, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990549, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990551, "dur": 43, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990596, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990597, "dur": 44, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990644, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990646, "dur": 40, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990688, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990690, "dur": 57, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990750, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990752, "dur": 55, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990809, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990811, "dur": 53, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990867, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990871, "dur": 49, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990922, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990924, "dur": 50, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990976, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085990978, "dur": 47, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991028, "dur": 54, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991084, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991086, "dur": 53, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991141, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991143, "dur": 56, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991202, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991204, "dur": 46, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991252, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991254, "dur": 53, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991309, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991311, "dur": 54, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991367, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991369, "dur": 39, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991412, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991460, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991461, "dur": 41, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991505, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991507, "dur": 41, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991550, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991552, "dur": 42, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991598, "dur": 42, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991642, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991644, "dur": 43, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991690, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991692, "dur": 41, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991736, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991738, "dur": 34, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991775, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991830, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991833, "dur": 38, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991873, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991875, "dur": 41, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991918, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991920, "dur": 42, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991965, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085991967, "dur": 38, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992007, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992009, "dur": 41, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992054, "dur": 42, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992098, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992100, "dur": 41, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992145, "dur": 138, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992285, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992287, "dur": 58, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992347, "dur": 3, "ph": "X", "name": "ProcessMessages 1484", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992351, "dur": 33, "ph": "X", "name": "ReadAsync 1484", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992386, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992388, "dur": 62, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992453, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992455, "dur": 58, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992516, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992518, "dur": 61, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992583, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992586, "dur": 57, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992647, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992651, "dur": 64, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992720, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992724, "dur": 63, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992791, "dur": 2, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992795, "dur": 46, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992845, "dur": 1, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992848, "dur": 56, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992908, "dur": 2, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992911, "dur": 54, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992968, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085992970, "dur": 48, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993021, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993023, "dur": 65, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993092, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993095, "dur": 57, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993155, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993156, "dur": 58, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993218, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993221, "dur": 59, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993282, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993284, "dur": 58, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993345, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993347, "dur": 56, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993407, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993410, "dur": 59, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993472, "dur": 2, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993475, "dur": 68, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993547, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993551, "dur": 70, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993624, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993627, "dur": 55, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993685, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993687, "dur": 58, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993748, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993750, "dur": 65, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993818, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993822, "dur": 60, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993884, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993887, "dur": 49, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993939, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993941, "dur": 49, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993993, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085993994, "dur": 53, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994051, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994053, "dur": 40, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994098, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994101, "dur": 59, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994165, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994167, "dur": 42, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994213, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994268, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994315, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994318, "dur": 44, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994364, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994366, "dur": 32, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994402, "dur": 111, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994518, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994567, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994569, "dur": 47, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994618, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994620, "dur": 34, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994658, "dur": 88, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994750, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994752, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994817, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994819, "dur": 47, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994870, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994872, "dur": 45, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994920, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085994922, "dur": 102, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995029, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995080, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995084, "dur": 57, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995144, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995147, "dur": 36, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995187, "dur": 77, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995269, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995322, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995324, "dur": 47, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995374, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995376, "dur": 47, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995427, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995430, "dur": 235, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995669, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995672, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995759, "dur": 2, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995763, "dur": 85, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995854, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995858, "dur": 71, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995933, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085995936, "dur": 122, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996064, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996145, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996149, "dur": 67, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996220, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996224, "dur": 49, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996278, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996281, "dur": 186, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996473, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996477, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996599, "dur": 3, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996603, "dur": 164, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996775, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996843, "dur": 2, "ph": "X", "name": "ProcessMessages 1065", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996847, "dur": 140, "ph": "X", "name": "ReadAsync 1065", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085996994, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997065, "dur": 2, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997068, "dur": 46, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997119, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997122, "dur": 133, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997259, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997262, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997325, "dur": 2, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997329, "dur": 53, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997386, "dur": 2, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997389, "dur": 84, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997478, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997538, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997541, "dur": 110, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997655, "dur": 2, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997658, "dur": 169, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997835, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997907, "dur": 4, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085997913, "dur": 133, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998053, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998129, "dur": 2, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998133, "dur": 51, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998188, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998191, "dur": 82, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998277, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998279, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998334, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998337, "dur": 89, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998428, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998430, "dur": 40, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998475, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998526, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998528, "dur": 95, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998628, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998680, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998683, "dur": 46, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998731, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998732, "dur": 34, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998771, "dur": 113, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998888, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998942, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998945, "dur": 48, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998995, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085998998, "dur": 109, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999111, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999163, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999165, "dur": 57, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999225, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999227, "dur": 42, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999272, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999274, "dur": 69, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999347, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999392, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999393, "dur": 40, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999436, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999437, "dur": 38, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999478, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999479, "dur": 103, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999586, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999588, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999684, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999687, "dur": 45, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999736, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999738, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999793, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999795, "dur": 47, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999845, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999847, "dur": 89, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999940, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401085999942, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000008, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000010, "dur": 86, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000100, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000102, "dur": 45, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000152, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000203, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000205, "dur": 51, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000259, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000262, "dur": 42, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000306, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000308, "dur": 56, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000367, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000369, "dur": 84, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000458, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000554, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000566, "dur": 49, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000618, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000620, "dur": 49, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000672, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000673, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000758, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000817, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000819, "dur": 53, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000875, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000877, "dur": 104, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086000985, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001044, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001046, "dur": 52, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001101, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001103, "dur": 108, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001215, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001271, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001273, "dur": 57, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001333, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001335, "dur": 56, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001394, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001396, "dur": 56, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001455, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001457, "dur": 49, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001509, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001511, "dur": 48, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001562, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001564, "dur": 83, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001652, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001745, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001747, "dur": 46, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001798, "dur": 51, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001852, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001854, "dur": 100, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086001959, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002016, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002018, "dur": 49, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002070, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002072, "dur": 49, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002123, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002125, "dur": 85, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002215, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002273, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002275, "dur": 56, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002333, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002335, "dur": 54, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002392, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002394, "dur": 91, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002489, "dur": 2, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002492, "dur": 52, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002549, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002599, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002602, "dur": 44, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002649, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002651, "dur": 97, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002753, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002811, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002815, "dur": 53, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002871, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002873, "dur": 47, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002923, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086002925, "dur": 94, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003024, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003065, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003068, "dur": 46, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003118, "dur": 38, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003158, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003160, "dur": 113, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003277, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003325, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003327, "dur": 45, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003374, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003375, "dur": 45, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003424, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003426, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003522, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003622, "dur": 2, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003626, "dur": 50, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003680, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003682, "dur": 49, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003736, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003740, "dur": 152, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003896, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086003900, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004018, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004022, "dur": 156, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004184, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004188, "dur": 74, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004268, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004272, "dur": 77, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004353, "dur": 2, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004357, "dur": 133, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004497, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004578, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004582, "dur": 67, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004654, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004659, "dur": 54, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004717, "dur": 2, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004721, "dur": 91, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004818, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004904, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004906, "dur": 50, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004959, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086004961, "dur": 52, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005016, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005018, "dur": 102, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005126, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005192, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005195, "dur": 54, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005253, "dur": 2, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005256, "dur": 89, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005350, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005408, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005411, "dur": 60, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005477, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005481, "dur": 51, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005535, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005537, "dur": 73, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005614, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005617, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005674, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005677, "dur": 53, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005734, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005737, "dur": 40, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005780, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005782, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005862, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005916, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005919, "dur": 52, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005974, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086005977, "dur": 45, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006026, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006028, "dur": 81, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006114, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006169, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006172, "dur": 57, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006232, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006235, "dur": 41, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006281, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006283, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006373, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006428, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006431, "dur": 56, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006491, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006493, "dur": 44, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006540, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006542, "dur": 105, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006653, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006710, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006712, "dur": 43, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006759, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006761, "dur": 101, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006867, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006919, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006923, "dur": 48, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006973, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086006975, "dur": 38, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007017, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007019, "dur": 78, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007102, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007158, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007160, "dur": 49, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007212, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007215, "dur": 100, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007319, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007377, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007380, "dur": 51, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007434, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007435, "dur": 99, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007539, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007598, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007601, "dur": 54, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007658, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007660, "dur": 100, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007763, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007813, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007817, "dur": 57, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007878, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007880, "dur": 76, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086007961, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008008, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008010, "dur": 44, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008056, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008058, "dur": 33, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008095, "dur": 97, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008195, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008240, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008242, "dur": 42, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008286, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008288, "dur": 36, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008328, "dur": 96, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008428, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008474, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008476, "dur": 38, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008516, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008520, "dur": 39, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008564, "dur": 75, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008643, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008706, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008709, "dur": 46, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008760, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008762, "dur": 66, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008833, "dur": 2, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008836, "dur": 51, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008890, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008893, "dur": 98, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008995, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086008997, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009049, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009052, "dur": 41, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009096, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009098, "dur": 41, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009142, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009144, "dur": 107, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009257, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009307, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009310, "dur": 48, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009361, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009364, "dur": 37, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009403, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009405, "dur": 73, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009483, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009534, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009537, "dur": 47, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009587, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009589, "dur": 37, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009629, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009631, "dur": 76, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009712, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009771, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009773, "dur": 45, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009821, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009824, "dur": 98, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009926, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009977, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086009980, "dur": 41, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010025, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010027, "dur": 27, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010056, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010058, "dur": 102, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010164, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010216, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010218, "dur": 48, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010270, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010272, "dur": 49, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010324, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010327, "dur": 48, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010378, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010381, "dur": 44, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010429, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010431, "dur": 45, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010479, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010481, "dur": 90, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010576, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010626, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010628, "dur": 56, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010688, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010690, "dur": 46, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010740, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010742, "dur": 49, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010794, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010796, "dur": 48, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010847, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010849, "dur": 39, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010891, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010893, "dur": 40, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010937, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086010939, "dur": 74, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011018, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011072, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011075, "dur": 50, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011128, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011130, "dur": 47, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011181, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011183, "dur": 48, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011234, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011237, "dur": 61, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011300, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011303, "dur": 42, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011349, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011351, "dur": 118, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011472, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011474, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011527, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011530, "dur": 101, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011636, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011639, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086011692, "dur": 518, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012214, "dur": 123, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012342, "dur": 10, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012354, "dur": 62, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012423, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012429, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012497, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012500, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012563, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012566, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012619, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012622, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012674, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012678, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012727, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012731, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012787, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012790, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012840, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012844, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012900, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012902, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012954, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086012958, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013012, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013016, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013069, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013072, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013144, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013147, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013224, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013226, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013296, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013300, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013347, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013351, "dur": 43, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013398, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013401, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013446, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013449, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013503, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013506, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013555, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013558, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013604, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013607, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013651, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013655, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013697, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013700, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013767, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013770, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013818, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013821, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013871, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013874, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013917, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013920, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013964, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086013968, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014013, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014016, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014062, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014067, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014108, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014111, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014178, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014181, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014232, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014235, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014277, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014280, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014323, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014326, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014369, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014372, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014414, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014417, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014459, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014462, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014511, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014514, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014559, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014562, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014614, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014616, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014663, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014666, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014709, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014712, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014756, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014759, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014804, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014807, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014850, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014853, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014896, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014898, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014930, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014933, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014974, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086014977, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015021, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015024, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015072, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015076, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015118, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015121, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015168, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015171, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015223, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015226, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015269, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015272, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015319, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015322, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015366, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015369, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015420, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015422, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015471, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015474, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015518, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015521, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015566, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015570, "dur": 39, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015613, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015616, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015661, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015665, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015717, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015721, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015767, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015771, "dur": 43, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015817, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015821, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015871, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015875, "dur": 46, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015924, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015928, "dur": 42, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015974, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086015977, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016026, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016029, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016077, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016080, "dur": 48, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016133, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016137, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016183, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016186, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016232, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016235, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016279, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016282, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016335, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016340, "dur": 52, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016396, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016399, "dur": 51, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016454, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016458, "dur": 41, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016503, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016506, "dur": 57, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016567, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016572, "dur": 46, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016620, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016622, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016761, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016763, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016806, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016809, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016853, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016857, "dur": 55, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016916, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016919, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016970, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086016973, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017017, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017022, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017069, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017072, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017117, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017120, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017164, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017167, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017211, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017214, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017267, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017270, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017305, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017307, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017347, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017350, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017396, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017399, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017444, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017447, "dur": 41, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017492, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017495, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017540, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017544, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017591, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017594, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017637, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017640, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017686, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017689, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017732, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017735, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017776, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017778, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017821, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017824, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017866, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017869, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017910, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017913, "dur": 38, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017954, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086017958, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018001, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018004, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018046, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018049, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018090, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018092, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018134, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018137, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018178, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018181, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018222, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018225, "dur": 41, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018270, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018273, "dur": 39, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018315, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018318, "dur": 37, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018358, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018362, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018404, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018407, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018452, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018454, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018505, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018509, "dur": 36, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018548, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018552, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018640, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018642, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018681, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018684, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018729, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018732, "dur": 207, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018943, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018947, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018992, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086018995, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086019039, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086019042, "dur": 145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086019191, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086019194, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086019241, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086019244, "dur": 6769, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026022, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026027, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026088, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026093, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026141, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026145, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026188, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026191, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026332, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026337, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026392, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086026396, "dur": 1176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086027577, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086027580, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086027628, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086027631, "dur": 217, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086027852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086027855, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086027902, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086027905, "dur": 172, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028084, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028127, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028130, "dur": 236, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028372, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028416, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028419, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028553, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028595, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086028598, "dur": 789, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029392, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029397, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029457, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029460, "dur": 217, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029682, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029684, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029738, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029741, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086029983, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030026, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030029, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030073, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030076, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030151, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030153, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030195, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030278, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030281, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030309, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030312, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030602, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030647, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030651, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030765, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030768, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030814, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030817, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030860, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030862, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030964, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086030967, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031011, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031014, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031055, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031057, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031176, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031216, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031219, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031264, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031268, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031292, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031329, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031331, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031371, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031373, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031414, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031417, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031453, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031455, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031497, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031499, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031642, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031644, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031681, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031684, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031722, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031724, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031769, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031805, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031808, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031866, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031907, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031910, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086031969, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032012, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032015, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032074, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032077, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032119, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032122, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032161, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032164, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032206, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032209, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032256, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032258, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032302, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032305, "dur": 414, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032724, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032772, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032775, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032823, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032826, "dur": 42, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032872, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032875, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032993, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086032995, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033035, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033039, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033087, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033090, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033253, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033256, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033295, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033297, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033339, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033342, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033383, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033386, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033576, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033578, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033623, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033626, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033670, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033673, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033712, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033715, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033765, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033767, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033809, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033812, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033949, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033989, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086033991, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034064, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034066, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034106, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034110, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034212, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034214, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034255, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034257, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034318, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034331, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034372, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034374, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034563, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034604, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034607, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034736, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034775, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034777, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034953, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034994, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086034996, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035110, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035146, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035148, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035252, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035292, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035294, "dur": 537, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035835, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035839, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035879, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035881, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035922, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086035924, "dur": 259, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086036187, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086036190, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086036229, "dur": 1076, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037311, "dur": 97, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037413, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037419, "dur": 69, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037492, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037496, "dur": 55, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037557, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037561, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037612, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037615, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037662, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037664, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037721, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037725, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037775, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037780, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037825, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037828, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037881, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037884, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037963, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086037966, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038019, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038023, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038131, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038134, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038180, "dur": 25, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038207, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038330, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038373, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038376, "dur": 185, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038566, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038568, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038616, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086038620, "dur": 746, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086039371, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086039373, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086039426, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086039429, "dur": 451, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086039884, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086039887, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086039934, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086039938, "dur": 438, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040380, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040383, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040427, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040429, "dur": 224, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040661, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040705, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040708, "dur": 187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040898, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040901, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040941, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040944, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040984, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086040988, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041069, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041072, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041118, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041121, "dur": 469, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041594, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041596, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041638, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041640, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041682, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041684, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041722, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086041724, "dur": 325, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042057, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042102, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042105, "dur": 273, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042382, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042384, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042425, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042542, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042596, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042598, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042645, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042647, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042704, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042706, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042757, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042795, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086042797, "dur": 222, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043024, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043026, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043066, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043069, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043105, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043108, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043144, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043146, "dur": 224, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043374, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043376, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043414, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043417, "dur": 181, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043605, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043644, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043647, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043693, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043697, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043735, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043737, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043778, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043781, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043837, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043839, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043880, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086043883, "dur": 494, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044381, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044384, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044425, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044428, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044467, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044469, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044508, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044511, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044548, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044559, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044703, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044705, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044741, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044743, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044815, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044817, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044867, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044869, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044910, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086044912, "dur": 1541, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086046462, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086046466, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086046544, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086046547, "dur": 202, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086046754, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086046757, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086046799, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086046802, "dur": 196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047005, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047045, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047047, "dur": 229, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047281, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047283, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047333, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047336, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047390, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047439, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047442, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047517, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047519, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047558, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047561, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047597, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047599, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047635, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047637, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047839, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047886, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047888, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047932, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047935, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047978, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086047981, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048161, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048163, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048203, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048205, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048270, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048272, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048314, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048316, "dur": 225, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048548, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048585, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086048587, "dur": 687, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049282, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049334, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049338, "dur": 121, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049462, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049465, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049510, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049513, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049564, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049567, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049611, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049614, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049700, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049746, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049749, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049811, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049813, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049867, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086049870, "dur": 193, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086050068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086050070, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086050118, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086050120, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086050383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086050385, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086050446, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086050449, "dur": 615, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051069, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051072, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051105, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051108, "dur": 228, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051340, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051347, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051397, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051401, "dur": 483, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051889, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051892, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051944, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086051947, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052057, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052059, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052122, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052124, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052178, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052180, "dur": 160, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052344, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052347, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052399, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052402, "dur": 392, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052798, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052801, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052858, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086052860, "dur": 218, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086053082, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086053085, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086053132, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086053135, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086053188, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086053190, "dur": 1535, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086054736, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086054741, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086054814, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086054820, "dur": 89041, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086143870, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086143874, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086143945, "dur": 3072, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086147026, "dur": 4529, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086151564, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086151570, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086151606, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086151608, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086151887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086151891, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086151930, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086151933, "dur": 256, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086152194, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086152229, "dur": 299, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086152531, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086152533, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086152583, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086152660, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086152697, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086152700, "dur": 1868, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086154576, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086154581, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086154641, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086154644, "dur": 537, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086155185, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086155188, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086155228, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086155230, "dur": 786, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156020, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156022, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156079, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156253, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156255, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156290, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156291, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156416, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156418, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156452, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086156455, "dur": 607, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086157066, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086157068, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086157104, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086157105, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086157153, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086157156, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086157199, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086157202, "dur": 1040, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158249, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158287, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158289, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158411, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158413, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158457, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158459, "dur": 430, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158895, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158931, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158933, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086158986, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086159023, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086159025, "dur": 659, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086159689, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086159729, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086159732, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086159947, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086159985, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086159987, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086160209, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086160211, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086160259, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086160261, "dur": 727, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086160991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086160993, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161028, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161030, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161189, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161223, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161226, "dur": 285, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161516, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161552, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161553, "dur": 366, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161923, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161925, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161961, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086161963, "dur": 419, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086162388, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086162391, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086162433, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086162435, "dur": 1588, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164031, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164034, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164071, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164075, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164209, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164269, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164273, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164365, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164367, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164413, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164416, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164467, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086164470, "dur": 605, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165080, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165084, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165123, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165126, "dur": 607, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165737, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165740, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165777, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165780, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165829, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165831, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165868, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086165871, "dur": 1036, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086166910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086166913, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086166959, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086166961, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167090, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167092, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167129, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167131, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167269, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167309, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167312, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167373, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167409, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167411, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167677, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167712, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086167714, "dur": 723, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086168441, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086168443, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086168465, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086168467, "dur": 1012, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086169483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086169485, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086169524, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086169526, "dur": 457, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086169987, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086169990, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086170032, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086170035, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086170152, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086170154, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086170194, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086170196, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086170320, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086170365, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086170368, "dur": 916, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171291, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171295, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171379, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171382, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171543, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171546, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171621, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171624, "dur": 341, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086171972, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086172014, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086172017, "dur": 1029, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173052, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173094, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173096, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173208, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173211, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173250, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173253, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173290, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086173293, "dur": 1095, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086174392, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086174395, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086174425, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086174427, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086174531, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086174533, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086174576, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086174579, "dur": 934, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175517, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175519, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175564, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175568, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175611, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175614, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175671, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175674, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175723, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175725, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175769, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175772, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175809, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175811, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175857, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175859, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175901, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175903, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175948, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086175951, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176001, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176005, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176054, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176058, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176101, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176105, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176151, "dur": 12, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176165, "dur": 39, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176207, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176211, "dur": 39, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176254, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176257, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176302, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176305, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176347, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176350, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176391, "dur": 1, "ph": "X", "name": "ProcessMessages 29", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176418, "dur": 46, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176468, "dur": 4, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176474, "dur": 41, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176519, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176521, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176565, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176568, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176630, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176634, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176681, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176685, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176732, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176736, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176782, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176786, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176830, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176833, "dur": 42, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176880, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176883, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176932, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176935, "dur": 48, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176988, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086176992, "dur": 216, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086177212, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086177215, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086177265, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086177268, "dur": 70, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086177343, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086177345, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086177406, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086177409, "dur": 175161, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086352579, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086352583, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086352649, "dur": 26, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086352677, "dur": 3363, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086356051, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086356056, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086356120, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086356123, "dur": 53782, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086409915, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086409918, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086409970, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086409974, "dur": 114, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086410092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086410095, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086410144, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086410148, "dur": 141041, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086551201, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086551206, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086551293, "dur": 28, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086551323, "dur": 5612, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086556947, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086556953, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086557029, "dur": 33, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086557065, "dur": 8417, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086565493, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086565497, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086565559, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086565562, "dur": 3650, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086569224, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086569228, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086569288, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086569293, "dur": 2752, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086572057, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086572065, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086572135, "dur": 38, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086572175, "dur": 35073, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086607259, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086607263, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086607328, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086607333, "dur": 1935, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086609277, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086609282, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086609357, "dur": 31, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086609391, "dur": 497, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086609893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086609896, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086609959, "dur": 432, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401086610396, "dur": 14729, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5212, "tid": 101, "ts": 1751401086650609, "dur": 6495, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5212, "tid": 8589934592, "ts": 1751401085950523, "dur": 192984, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751401086143511, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751401086143525, "dur": 1406, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5212, "tid": 101, "ts": 1751401086657107, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5212, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401085924475, "dur": 702491, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401085928705, "dur": 11979, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401086627299, "dur": 8231, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401086632814, "dur": 199, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401086635661, "dur": 22, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5212, "tid": 101, "ts": 1751401086657118, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751401085962899, "dur": 89, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401085963131, "dur": 4236, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401085967397, "dur": 1382, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401085968981, "dur": 126, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751401085969108, "dur": 801, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401085970479, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085970562, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_7D12CF9E98D9D784.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085970903, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_BC34EA19774048D3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085971015, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_6BC1B44B0B58CEF0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085971240, "dur": 170, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FC3AA1C0027C0ADA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085971941, "dur": 361, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_94DD634DECCA9836.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085972491, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_622A1FF3909771E4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085972727, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_42529D1BD0B2CBE0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085973216, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_9923BABDF0F3077F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085973356, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085973451, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_C1FAB9A8897A5958.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085973569, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_82A68248955BDDC4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085973684, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_92A5F53C232CE1D9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085973836, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_1D34F993682102CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085974008, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085974432, "dur": 1979, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_1974DA72EEA9484D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085976641, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_26531C2D0F795B12.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085976799, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_B44D12B45B4CC90C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085977064, "dur": 149, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085977968, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085978421, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751401085978494, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751401085979477, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_7D2E9826DFBFBE48.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085979652, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_CE65D8670DA73F5C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085979782, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401085979846, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751401085979923, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751401085980458, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401085984679, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751401085987756, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751401085987868, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751401085987949, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401085988133, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751401085988236, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401085988293, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751401085988598, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751401085988683, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751401085988777, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401085990546, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401085993269, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751401085993481, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401085995508, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751401085996014, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751401085996355, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751401085996440, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751401085996509, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401085996907, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401085997915, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751401085998014, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751401085998810, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401085999085, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751401086000340, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751401086000765, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751401086001213, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751401086002409, "dur": 117, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751401086003150, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401086003786, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751401086004291, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401086004629, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751401086004689, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751401086004768, "dur": 130, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751401086005260, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751401086005631, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401086009369, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751401086009437, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751401086009508, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751401085969981, "dur": 42235, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401086012235, "dur": 597811, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401086610047, "dur": 135, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401086610183, "dur": 155, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401086610495, "dur": 69, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401086610605, "dur": 2012, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751401085970474, "dur": 41824, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086012306, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086012386, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086013756, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_611E434C56DD2756.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086014007, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086014133, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_92A5F53C232CE1D9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086014968, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086015031, "dur": 319, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751401086015761, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751401086015855, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086015924, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086016059, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086016152, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086016304, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086016696, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086016865, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086017057, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086017253, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086017581, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086017681, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086017752, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086017804, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086018089, "dur": 424, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401086019171, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086019489, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086019745, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086020543, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086020817, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086021153, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086021683, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086021976, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086022277, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086022578, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086022830, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086023072, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086023315, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086024030, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086024532, "dur": 775, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing\\PostProcessing\\Editor\\PostProcessProfileEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751401086024280, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086025640, "dur": 1098, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\BuildProcessors\\CoreBuildData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751401086025551, "dur": 1638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086027190, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086027445, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086027709, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086027969, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086028239, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086028879, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086029100, "dur": 3017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086032198, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086032431, "dur": 1463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086033895, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086034052, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086034302, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086034524, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086034798, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086035050, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086035291, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086035980, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086036566, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086037137, "dur": 2946, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086040143, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086040274, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086040724, "dur": 1838, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\RenderGraph\\RenderGraphBuilder.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751401086040711, "dur": 2132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086042843, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086043224, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086043577, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086043828, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086044441, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086045177, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086045469, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086046496, "dur": 1285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086047784, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086048050, "dur": 2177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086050301, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086050541, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086051809, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401086052078, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086052764, "dur": 97216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086149982, "dur": 2224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086152270, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086154747, "dur": 2392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086157156, "dur": 3472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086160683, "dur": 5829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086166566, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086169094, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086169175, "dur": 3438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086172615, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086172702, "dur": 3493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401086176301, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086176403, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086176490, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086176709, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086176795, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751401086176876, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751401086177083, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086177201, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751401086177417, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086177562, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751401086177691, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401086178102, "dur": 431906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401085970335, "dur": 41934, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086012283, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086012441, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_F3966D486516AA58.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086012566, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086013819, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086014033, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5911DF3198B3A870.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086015073, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751401086015164, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086015265, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751401086015363, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086015481, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751401086015554, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086015728, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751401086015991, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086016556, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751401086017169, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751401086017321, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086017561, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751401086017983, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751401086018500, "dur": 311, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751401086018907, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751401086019164, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086019457, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086019755, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086020071, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086020262, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086020532, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086020779, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086021069, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086021584, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086021972, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086022269, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086022574, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086022827, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086023067, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086023317, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086024021, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086024279, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086024583, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086024852, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086025099, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086025364, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086025658, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086025925, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086026198, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086026450, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086026707, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086027035, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086027342, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086027624, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086027892, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086028158, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086028517, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086028748, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086028811, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086030657, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086030766, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086031547, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086031772, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086032599, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086032851, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086033575, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086033770, "dur": 8963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086042813, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086043112, "dur": 2264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086045432, "dur": 1897, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\EditorBindingUtility.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751401086045432, "dur": 1911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086047344, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086047823, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086048007, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086048126, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086048925, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086049041, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401086049276, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086049914, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086050030, "dur": 104830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086154863, "dur": 2877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086157800, "dur": 3088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086160933, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086160998, "dur": 3953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086164952, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401086165181, "dur": 2779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086168038, "dur": 4154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086172235, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086172336, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086175119, "dur": 2768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401086177957, "dur": 432063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401085970314, "dur": 41939, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086012283, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086012417, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086012537, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086012975, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_B7F547A15B2C01B2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086013117, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0106BA1057A1C6CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086013192, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086013472, "dur": 444, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086013917, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A7F411FD3CF1CCBD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086014755, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086015061, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086015224, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401086015466, "dur": 639, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401086016297, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751401086016380, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086016745, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086017204, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086017285, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086018000, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401086018180, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751401086018261, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401086018457, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086018796, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401086019065, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401086019182, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086019430, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086019906, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086020252, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086020617, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086021092, "dur": 1150, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\Overrides\\ColorLookupEditor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751401086020901, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086022387, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086022645, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086022893, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086023137, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086023823, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086024080, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086024363, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086024673, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086024929, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086025285, "dur": 1605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\RemoveComponentUtils.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751401086025237, "dur": 2117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086027354, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086027623, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086027884, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086028144, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086029037, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086029317, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086030136, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086030454, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086030903, "dur": 950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086032058, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086032375, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086032741, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086032995, "dur": 2789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086035866, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086036588, "dur": 1070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086037790, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086037971, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086038313, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086038594, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086038690, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086039198, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086039484, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086039773, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086040311, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086040589, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086040856, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086041124, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086041400, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086041632, "dur": 6474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086048111, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086048322, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086048562, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086048625, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086050128, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086050222, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401086050426, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086051113, "dur": 99418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086150532, "dur": 2684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086153256, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086153320, "dur": 3618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086156991, "dur": 2583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086159630, "dur": 2571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086162251, "dur": 2799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086165114, "dur": 2909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086168024, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086168097, "dur": 2743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086170887, "dur": 3003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086173892, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086174008, "dur": 3152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086177161, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086177217, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401086177703, "dur": 391898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401086569637, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751401086569602, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751401086569929, "dur": 2825, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751401086572762, "dur": 37272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401085970457, "dur": 41826, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086012290, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086012547, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086012771, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2504973AF8D3F8E7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086013017, "dur": 350, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_DF7681D3753AB490.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086013404, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086013491, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_40E8F641E9958680.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086013732, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_69C31F9D75BAAB88.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086014000, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086014118, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_D8B62AD188B30A07.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086014777, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086015027, "dur": 734, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751401086015916, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086016048, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086016654, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086016860, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086016933, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086017167, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751401086017345, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086017945, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086018090, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751401086018231, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751401086018313, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086018390, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086018503, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086018601, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086018693, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086018783, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086019005, "dur": 421, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401086019428, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086019755, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086020030, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086020278, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086020536, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086020810, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086021529, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086021914, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086022219, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086022558, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086022904, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086023157, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086023876, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086024146, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086024603, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086024880, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086025315, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086025771, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086026034, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086026301, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086026575, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086026905, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086027252, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086027535, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086027804, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086028085, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086028360, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086028584, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086031365, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086031695, "dur": 2683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086034470, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401086034807, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086035471, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751401086036312, "dur": 169, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086036966, "dur": 107633, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751401086149968, "dur": 2587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086152617, "dur": 2579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086155276, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086157888, "dur": 2480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086160423, "dur": 2608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086163033, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086163111, "dur": 2637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086165809, "dur": 2542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086168413, "dur": 2592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086171056, "dur": 2836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086173946, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401086176873, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.FilmInternalUtilities.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751401086176995, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751401086177188, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751401086177508, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751401086177689, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401086177970, "dur": 432065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401085970606, "dur": 41718, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086012332, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_1AA5CC13572AAA47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086012386, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086012550, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086013154, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0C31EB717FD7AB62.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086013300, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_034D57ECFF8286E4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086013421, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086013556, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086013687, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0D58F8E6C2E92DF4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086013820, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086015319, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_0B080C2548A382FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086015669, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751401086015888, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751401086016084, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086016446, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751401086016963, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751401086017261, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086017577, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751401086017774, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751401086018178, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751401086018261, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751401086019225, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086019616, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086020014, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086020266, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086020517, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086020767, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086021083, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086021505, "dur": 1842, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\2D\\ShapeEditor\\GUIFramework\\GUIAction.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751401086023347, "dur": 974, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\2D\\ShapeEditor\\GUIFramework\\GenericDefaultControl.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751401086021444, "dur": 3105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086024550, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086024819, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086025071, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086025329, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086025607, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086025883, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086026151, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086026405, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086026664, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086026965, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086027320, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086027593, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086027854, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086028111, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086028382, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086028579, "dur": 3452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086032067, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Runtime.ref.dll_3152B6A9836FBF3B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086032150, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086032501, "dur": 886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086033497, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086034474, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086034944, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086035611, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086035709, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086036371, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086036620, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086037222, "dur": 1560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086038784, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086038889, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086039235, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086039511, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086039791, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086040043, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086040322, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086040792, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086041081, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086041479, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086041685, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086042412, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086042606, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086042859, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086043159, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086043483, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086043754, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086044372, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086044481, "dur": 361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086044842, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086045129, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086045488, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086046106, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086046490, "dur": 1546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086048037, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086048664, "dur": 4884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086053551, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401086053870, "dur": 96099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086149977, "dur": 2607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086152634, "dur": 3229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086155918, "dur": 3168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086159149, "dur": 2521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086161722, "dur": 2949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086164673, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086164744, "dur": 3023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086167825, "dur": 2811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086170637, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086170723, "dur": 5592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086176570, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086177011, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086177200, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751401086177566, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751401086177698, "dur": 178089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086355842, "dur": 48537, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751401086355795, "dur": 51587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086410538, "dur": 264, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401086411650, "dur": 146003, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401086569601, "dur": 38312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751401086569590, "dur": 38324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751401086607945, "dur": 2023, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751401085970818, "dur": 41522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086012346, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CCD605E26FC2B53B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086012551, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086013025, "dur": 510, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DE036AB7E94B4D31.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086013555, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_7049A087AD26B808.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086013727, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086014348, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086014870, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086015071, "dur": 4550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086019707, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086019918, "dur": 6745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086026771, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086027057, "dur": 1187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086028358, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086028589, "dur": 1938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086030661, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086031025, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086031649, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086031721, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086031954, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086032714, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086032839, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086032933, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_E2F92DAB6C167CC9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086032993, "dur": 10263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086043258, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086043313, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086043494, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086043797, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086044602, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086044894, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086045590, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086046088, "dur": 135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086046337, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086046489, "dur": 1556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086048046, "dur": 3795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086051843, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401086052069, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086052634, "dur": 97798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086150434, "dur": 2437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086152928, "dur": 5679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086158609, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086158977, "dur": 2900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086161924, "dur": 2963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086164926, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086164999, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086167642, "dur": 2528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086170218, "dur": 3502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086173785, "dur": 2842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401086176697, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086177034, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751401086177096, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086177221, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751401086177453, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086177700, "dur": 388003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401086565730, "dur": 395, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751401086565705, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751401086566174, "dur": 43875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401085970988, "dur": 41366, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086012358, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BF2CDC1257CA045F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086012544, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086013269, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_91801C7D6E952EB7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086013578, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086013722, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_681D101710621A59.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086013932, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086013996, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_681D101710621A59.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086014607, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_C9E7F9647D79AD26.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086014767, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086014891, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086015089, "dur": 3823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086019159, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751401086019372, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086019640, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086019931, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086020153, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086020395, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086020654, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086020927, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086021702, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086022009, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086022328, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086022597, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086022841, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086023094, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086023814, "dur": 723, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Interfaces\\IMayRequireViewDirection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751401086023767, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086024744, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086025001, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086025269, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086025565, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086025853, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086026116, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086026377, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086026686, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086027065, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086027313, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086027567, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086027833, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086028089, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086028380, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086028594, "dur": 12462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086041145, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086041385, "dur": 1824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086043255, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086043371, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_CE6A42C97D96EB0A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086043437, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086044111, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086045113, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086045272, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086045539, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086045603, "dur": 2065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086047778, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401086048011, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086048824, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086048925, "dur": 96733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086148163, "dur": 394, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751401086148559, "dur": 1766, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1751401086150326, "dur": 181, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1751401086145660, "dur": 4848, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086150509, "dur": 2798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086153308, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086153398, "dur": 3294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086156737, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086156818, "dur": 2857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086159722, "dur": 2882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086162650, "dur": 3766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086166469, "dur": 2564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086169034, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086169208, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086171986, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086172098, "dur": 2903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086175002, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401086175266, "dur": 2751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401086178090, "dur": 431925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401085971169, "dur": 41256, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086012553, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086013836, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_EA250D0B58FB5B36.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086014656, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086014929, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086015099, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086015309, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_7D2E9826DFBFBE48.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086015505, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086015632, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086015689, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086016040, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086016128, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086016450, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086016638, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086016736, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086017258, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086017638, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086017948, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086018633, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086018866, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086018959, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086019057, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086019125, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751401086019230, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086019889, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086020872, "dur": 2915, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\Image.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751401086020221, "dur": 3595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086023817, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086024070, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086024324, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086024604, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086024869, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086025126, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086025649, "dur": 1356, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\Camera\\CameraUI.Output.Skin.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751401086027149, "dur": 1631, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\Camera\\CameraUI.Environment.Skin.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751401086028781, "dur": 2753, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\Camera\\CameraUI.Environment.Drawers.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751401086025408, "dur": 6127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086031536, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_0524057423981A9D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086032219, "dur": 1494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086033726, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086034082, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086034353, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086034659, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086036919, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086037539, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086037734, "dur": 1246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086038982, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086039086, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086039297, "dur": 1264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086040615, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086040959, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086041571, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086041801, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086042263, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086042322, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086042550, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086042820, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086043096, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086043356, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086043868, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086044105, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086044326, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086045152, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086045428, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086045631, "dur": 1441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086047074, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086047201, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086047480, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086048255, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086048346, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086048589, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086048644, "dur": 1815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086050460, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086050552, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086050792, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086051838, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086052065, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086052885, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086053067, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086053420, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086053539, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401086053807, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086055249, "dur": 149, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086056668, "dur": 296628, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086356314, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751401086355779, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086356739, "dur": 47618, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751401086356736, "dur": 50646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086410010, "dur": 318, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401086411310, "dur": 140607, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401086565713, "dur": 428, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751401086565696, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751401086566177, "dur": 43873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401086620125, "dur": 5042, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5212, "tid": 101, "ts": 1751401086657702, "dur": 3682, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5212, "tid": 101, "ts": 1751401086661494, "dur": 5885, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5212, "tid": 101, "ts": 1751401086646051, "dur": 26244, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}