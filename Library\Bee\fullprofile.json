{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5212, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5212, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5212, "tid": 128, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5212, "tid": 128, "ts": 1751401448896905, "dur": 1501, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5212, "tid": 128, "ts": 1751401448902918, "dur": 1015, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5212, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5212, "tid": 1, "ts": 1751401448359883, "dur": 6947, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751401448366834, "dur": 79161, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751401448446013, "dur": 46907, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5212, "tid": 128, "ts": 1751401448903939, "dur": 25, "ph": "X", "name": "", "args": {}}, {"pid": 5212, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448357697, "dur": 10340, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448368042, "dur": 516393, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448369625, "dur": 3302, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448372939, "dur": 2274, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448375217, "dur": 358, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448375579, "dur": 24, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448375605, "dur": 138, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448375747, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448375750, "dur": 523, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376277, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376280, "dur": 193, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376477, "dur": 4, "ph": "X", "name": "ProcessMessages 5668", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376482, "dur": 66, "ph": "X", "name": "ReadAsync 5668", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376552, "dur": 1, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376555, "dur": 132, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376690, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376693, "dur": 49, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376746, "dur": 2, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376750, "dur": 129, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376883, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448376885, "dur": 164, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377052, "dur": 1, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377056, "dur": 44, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377102, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377104, "dur": 93, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377203, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377253, "dur": 1, "ph": "X", "name": "ProcessMessages 1347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377255, "dur": 84, "ph": "X", "name": "ReadAsync 1347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377345, "dur": 46, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377393, "dur": 1, "ph": "X", "name": "ProcessMessages 1197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377395, "dur": 50, "ph": "X", "name": "ReadAsync 1197", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377448, "dur": 37, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377488, "dur": 36, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377528, "dur": 36, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377568, "dur": 43, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377613, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377615, "dur": 23, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377642, "dur": 33, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377678, "dur": 43, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377724, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377725, "dur": 34, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377764, "dur": 42, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377810, "dur": 39, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377854, "dur": 41, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377898, "dur": 28, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377927, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377929, "dur": 42, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448377974, "dur": 36, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378014, "dur": 43, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378059, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378060, "dur": 40, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378104, "dur": 40, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378146, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378148, "dur": 43, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378195, "dur": 37, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378234, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378235, "dur": 37, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378276, "dur": 43, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378322, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378323, "dur": 43, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378370, "dur": 38, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378410, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378411, "dur": 37, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378453, "dur": 40, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378496, "dur": 43, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378541, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378542, "dur": 42, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378588, "dur": 39, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378630, "dur": 40, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378678, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378680, "dur": 33, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378717, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378762, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378763, "dur": 40, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378807, "dur": 42, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378851, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378852, "dur": 48, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378903, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378904, "dur": 34, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378942, "dur": 37, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448378982, "dur": 43, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379026, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379028, "dur": 41, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379073, "dur": 43, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379118, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379120, "dur": 35, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379158, "dur": 60, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379222, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379225, "dur": 45, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379273, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379276, "dur": 58, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379336, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379339, "dur": 41, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379383, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379385, "dur": 179, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379568, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379570, "dur": 45, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379618, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379621, "dur": 76, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379701, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379703, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379752, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379754, "dur": 64, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379821, "dur": 1, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379824, "dur": 56, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379883, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379886, "dur": 45, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379934, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379938, "dur": 59, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448379999, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380002, "dur": 64, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380069, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380071, "dur": 48, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380123, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380125, "dur": 36, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380166, "dur": 48, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380217, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380221, "dur": 47, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380270, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380273, "dur": 46, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380321, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380324, "dur": 41, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380367, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380369, "dur": 175, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380549, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380599, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380602, "dur": 45, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380649, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380651, "dur": 41, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380694, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380695, "dur": 39, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380737, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380739, "dur": 32, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380774, "dur": 35, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380814, "dur": 38, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380854, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380856, "dur": 39, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380898, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380900, "dur": 38, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380940, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380942, "dur": 39, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380984, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448380986, "dur": 30, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381019, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381063, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381065, "dur": 41, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381108, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381110, "dur": 40, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381153, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381155, "dur": 39, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381198, "dur": 31, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381231, "dur": 1, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381233, "dur": 34, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381271, "dur": 39, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381313, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381314, "dur": 38, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381355, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381357, "dur": 39, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381398, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381401, "dur": 42, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381446, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381447, "dur": 30, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381482, "dur": 39, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381524, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381525, "dur": 37, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381567, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381569, "dur": 38, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381609, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381611, "dur": 34, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381648, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381649, "dur": 34, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381688, "dur": 39, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381731, "dur": 38, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381771, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381773, "dur": 38, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381814, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381815, "dur": 38, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381856, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381858, "dur": 32, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381894, "dur": 39, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381937, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381939, "dur": 39, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381981, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448381982, "dur": 40, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382025, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382027, "dur": 35, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382066, "dur": 35, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382104, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382105, "dur": 37, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382145, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382146, "dur": 38, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382188, "dur": 38, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382229, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382231, "dur": 40, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382273, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382275, "dur": 35, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382312, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382314, "dur": 38, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382356, "dur": 39, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382397, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382398, "dur": 37, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382438, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382440, "dur": 38, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382481, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382482, "dur": 39, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382525, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382527, "dur": 33, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382564, "dur": 47, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382614, "dur": 45, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382661, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382663, "dur": 41, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382715, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382718, "dur": 39, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382759, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382761, "dur": 39, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382804, "dur": 43, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382851, "dur": 41, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382895, "dur": 42, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382939, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382942, "dur": 38, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382982, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448382984, "dur": 18, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383004, "dur": 37, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383044, "dur": 41, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383088, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383089, "dur": 40, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383133, "dur": 40, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383175, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383176, "dur": 42, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383221, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383223, "dur": 45, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383270, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383272, "dur": 48, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383324, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383326, "dur": 42, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383370, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383372, "dur": 37, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383412, "dur": 44, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383459, "dur": 42, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383503, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383505, "dur": 42, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383549, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383550, "dur": 43, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383597, "dur": 34, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383634, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383678, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383680, "dur": 43, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383725, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383726, "dur": 43, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383771, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383773, "dur": 38, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383814, "dur": 41, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383858, "dur": 42, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383901, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383903, "dur": 42, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383949, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383951, "dur": 39, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383992, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448383994, "dur": 40, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384038, "dur": 42, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384082, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384085, "dur": 42, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384129, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384131, "dur": 43, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384176, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384178, "dur": 38, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384219, "dur": 44, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384265, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384267, "dur": 43, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384312, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384313, "dur": 42, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384357, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384359, "dur": 41, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384403, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384405, "dur": 54, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384465, "dur": 50, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384518, "dur": 9, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384528, "dur": 40, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384571, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384573, "dur": 35, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384610, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384612, "dur": 30, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384645, "dur": 35, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384684, "dur": 50, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384737, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384740, "dur": 52, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384794, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384796, "dur": 43, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384841, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384843, "dur": 34, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384880, "dur": 41, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384925, "dur": 41, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384969, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448384970, "dur": 41, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385015, "dur": 40, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385057, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385059, "dur": 36, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385097, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385099, "dur": 49, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385151, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385153, "dur": 41, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385198, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385199, "dur": 38, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385239, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385241, "dur": 33, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385276, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385278, "dur": 43, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385324, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385363, "dur": 41, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385408, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385410, "dur": 51, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385463, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385465, "dur": 41, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385508, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385517, "dur": 43, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385562, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385564, "dur": 40, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385606, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385608, "dur": 42, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385652, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385669, "dur": 46, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385718, "dur": 193, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448385913, "dur": 87, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386003, "dur": 3, "ph": "X", "name": "ProcessMessages 3440", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386006, "dur": 33, "ph": "X", "name": "ReadAsync 3440", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386041, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386044, "dur": 41, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386088, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386090, "dur": 46, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386138, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386140, "dur": 43, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386186, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386188, "dur": 47, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386237, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386241, "dur": 49, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386293, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386295, "dur": 51, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386349, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386350, "dur": 46, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386398, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386400, "dur": 42, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386443, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386445, "dur": 32, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386480, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386520, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386522, "dur": 46, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386572, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386574, "dur": 40, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386616, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386618, "dur": 37, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386659, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386660, "dur": 35, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386697, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386699, "dur": 30, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386732, "dur": 33, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386768, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386770, "dur": 50, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386823, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386825, "dur": 50, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386877, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386879, "dur": 38, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386921, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386966, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448386968, "dur": 58, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387033, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387035, "dur": 49, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387088, "dur": 2, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387091, "dur": 45, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387139, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387141, "dur": 34, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387178, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387180, "dur": 39, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387221, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387223, "dur": 38, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387264, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387266, "dur": 41, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387311, "dur": 36, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387350, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387352, "dur": 46, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387401, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387402, "dur": 40, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387446, "dur": 1, "ph": "X", "name": "ProcessMessages 97", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387448, "dur": 46, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387496, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387498, "dur": 49, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387552, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387555, "dur": 50, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387609, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387612, "dur": 105, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387723, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387725, "dur": 60, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387789, "dur": 2, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387792, "dur": 109, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387907, "dur": 52, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387962, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448387965, "dur": 57, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388025, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388028, "dur": 49, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388080, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388083, "dur": 58, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388144, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388147, "dur": 56, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388207, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388210, "dur": 63, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388277, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388280, "dur": 54, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388338, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388341, "dur": 36, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388380, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388383, "dur": 47, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388433, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388436, "dur": 44, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388483, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388486, "dur": 41, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388532, "dur": 52, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388588, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388591, "dur": 55, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388650, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388653, "dur": 54, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388710, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388713, "dur": 57, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388773, "dur": 1, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388776, "dur": 56, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388835, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388838, "dur": 61, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388901, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388904, "dur": 54, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388961, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448388963, "dur": 49, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389015, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389017, "dur": 51, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389071, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389073, "dur": 196, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389272, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389275, "dur": 75, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389353, "dur": 3, "ph": "X", "name": "ProcessMessages 2160", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389358, "dur": 49, "ph": "X", "name": "ReadAsync 2160", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389412, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389414, "dur": 35, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389451, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389453, "dur": 36, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389492, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389493, "dur": 35, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389530, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389532, "dur": 35, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389571, "dur": 35, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389608, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389610, "dur": 36, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389650, "dur": 45, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389698, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389700, "dur": 43, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389745, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389747, "dur": 38, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389787, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389789, "dur": 33, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389826, "dur": 26, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389856, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389892, "dur": 35, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389929, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389930, "dur": 31, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389964, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448389966, "dur": 46, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390015, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390017, "dur": 47, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390066, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390068, "dur": 43, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390114, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390116, "dur": 40, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390159, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390162, "dur": 40, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390206, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390253, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390298, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390300, "dur": 50, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390353, "dur": 33, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390389, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390391, "dur": 70, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390464, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390505, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390506, "dur": 37, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390547, "dur": 39, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390588, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390590, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390674, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390723, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390726, "dur": 43, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390770, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390773, "dur": 35, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390812, "dur": 67, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390884, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390939, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390942, "dur": 44, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390988, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448390990, "dur": 74, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391067, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391112, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391116, "dur": 43, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391162, "dur": 34, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391200, "dur": 65, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391268, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391314, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391316, "dur": 43, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391362, "dur": 33, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391398, "dur": 63, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391464, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391508, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391509, "dur": 58, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391570, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391572, "dur": 37, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391613, "dur": 42, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391658, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391703, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391705, "dur": 42, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391751, "dur": 34, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391788, "dur": 62, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391854, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391898, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391900, "dur": 42, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391945, "dur": 33, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448391982, "dur": 65, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392050, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392095, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392097, "dur": 43, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392145, "dur": 35, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392184, "dur": 59, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392246, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392288, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392290, "dur": 43, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392336, "dur": 34, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392374, "dur": 62, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392438, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392483, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392485, "dur": 42, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392530, "dur": 33, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392565, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392567, "dur": 67, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392638, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392683, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392685, "dur": 42, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392730, "dur": 33, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392767, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392829, "dur": 11, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392842, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392894, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392896, "dur": 41, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392939, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448392941, "dur": 76, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393022, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393072, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393074, "dur": 41, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393117, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393119, "dur": 36, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393159, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393161, "dur": 61, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393226, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393274, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393276, "dur": 39, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393318, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393320, "dur": 85, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393410, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393457, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393459, "dur": 43, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393504, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393506, "dur": 77, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393588, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393639, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393641, "dur": 39, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393682, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393686, "dur": 83, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393773, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393813, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393815, "dur": 44, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393862, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393864, "dur": 71, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393939, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393990, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448393992, "dur": 41, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394037, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394039, "dur": 49, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394092, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394094, "dur": 70, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394167, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394210, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394212, "dur": 39, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394254, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394256, "dur": 50, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394309, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394311, "dur": 47, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394360, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394362, "dur": 43, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394408, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394410, "dur": 42, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394455, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394457, "dur": 48, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394508, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394510, "dur": 44, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394557, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394605, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394608, "dur": 51, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394661, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394664, "dur": 68, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394735, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394784, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394786, "dur": 53, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394842, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394844, "dur": 70, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394918, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394971, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448394974, "dur": 45, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395021, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395023, "dur": 72, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395099, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395145, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395147, "dur": 51, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395201, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395203, "dur": 43, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395248, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395250, "dur": 44, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395295, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395298, "dur": 37, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395338, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395340, "dur": 41, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395383, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395385, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395439, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395479, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395513, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395514, "dur": 41, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395558, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395559, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395625, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395665, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395666, "dur": 40, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395710, "dur": 40, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395754, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395756, "dur": 83, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395844, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395896, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395899, "dur": 59, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395962, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448395964, "dur": 53, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396020, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396023, "dur": 169, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396196, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396199, "dur": 58, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396260, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396263, "dur": 95, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396363, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396413, "dur": 2, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396417, "dur": 49, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396470, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396473, "dur": 124, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396604, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396663, "dur": 2, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396667, "dur": 99, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396771, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396844, "dur": 2, "ph": "X", "name": "ProcessMessages 1078", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396848, "dur": 45, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396897, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396899, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396954, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448396957, "dur": 48, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397008, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397011, "dur": 110, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397125, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397128, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397203, "dur": 3, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397207, "dur": 114, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397325, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397328, "dur": 150, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397481, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397483, "dur": 45, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397530, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397532, "dur": 38, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397572, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397575, "dur": 102, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397681, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397764, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397767, "dur": 32, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397804, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397849, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397931, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397933, "dur": 43, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397978, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448397980, "dur": 81, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398065, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398154, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398156, "dur": 38, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398196, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398198, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398237, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398284, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398286, "dur": 40, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398329, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398331, "dur": 81, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398416, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398457, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398459, "dur": 37, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398499, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398500, "dur": 30, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398533, "dur": 72, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398609, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398650, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398652, "dur": 36, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398690, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398692, "dur": 32, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398728, "dur": 75, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398805, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398840, "dur": 44, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398885, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398888, "dur": 34, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398925, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448398927, "dur": 82, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399012, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399052, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399053, "dur": 35, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399090, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399092, "dur": 32, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399126, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399129, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399206, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399246, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399248, "dur": 35, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399288, "dur": 32, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399324, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399401, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399442, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399444, "dur": 37, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399482, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399485, "dur": 30, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399518, "dur": 81, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399603, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399642, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399644, "dur": 35, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399681, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399684, "dur": 32, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399719, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399720, "dur": 78, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399804, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399844, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399845, "dur": 36, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399884, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399886, "dur": 31, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448399921, "dur": 70, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400001, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400041, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400043, "dur": 39, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400085, "dur": 30, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400119, "dur": 68, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400191, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400234, "dur": 38, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400273, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400275, "dur": 31, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400310, "dur": 97, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400410, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400454, "dur": 35, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400491, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400493, "dur": 32, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400528, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400604, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400702, "dur": 1, "ph": "X", "name": "ProcessMessages 1201", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400704, "dur": 72, "ph": "X", "name": "ReadAsync 1201", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400779, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400818, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400821, "dur": 38, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400862, "dur": 29, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400895, "dur": 71, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448400970, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401010, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401012, "dur": 33, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401048, "dur": 33, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401085, "dur": 85, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401174, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401216, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401218, "dur": 37, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401257, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401259, "dur": 31, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401294, "dur": 65, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401362, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401403, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401404, "dur": 34, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401441, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401442, "dur": 31, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401476, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401477, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401562, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401602, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401604, "dur": 36, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401642, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401644, "dur": 32, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401679, "dur": 75, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401757, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401798, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401800, "dur": 33, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401836, "dur": 33, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401873, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401944, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401983, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448401984, "dur": 32, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402019, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402021, "dur": 39, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402062, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402064, "dur": 35, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402102, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402104, "dur": 34, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402140, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402142, "dur": 32, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402178, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402204, "dur": 86, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402292, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402295, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402335, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402337, "dur": 35, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402376, "dur": 37, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402415, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402417, "dur": 32, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402451, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402453, "dur": 38, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402493, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402495, "dur": 36, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402533, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402535, "dur": 31, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402570, "dur": 32, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402606, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402687, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402726, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402728, "dur": 36, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402767, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402768, "dur": 37, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402810, "dur": 36, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402848, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402850, "dur": 36, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402891, "dur": 34, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402926, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402928, "dur": 32, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448402964, "dur": 100, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403068, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403110, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403112, "dur": 91, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403207, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403210, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403258, "dur": 400, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403662, "dur": 82, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403748, "dur": 9, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403759, "dur": 43, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403806, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403809, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403852, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403855, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403890, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403947, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403984, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448403987, "dur": 33, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404023, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404026, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404060, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404063, "dur": 118, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404185, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404187, "dur": 30, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404220, "dur": 3, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404224, "dur": 51, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404281, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404316, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404318, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404351, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404353, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404388, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404555, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404558, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404606, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404610, "dur": 35, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404649, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404651, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404685, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404720, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404755, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404785, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404817, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404853, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404856, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404893, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404897, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404930, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404933, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404969, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448404972, "dur": 103, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405082, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405119, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405121, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405155, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405187, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405190, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405249, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405251, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405289, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405292, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405339, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405342, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405379, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405414, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405445, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405447, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405479, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405481, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405517, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405552, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405554, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405588, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405590, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405623, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405625, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405659, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405692, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405738, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405780, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405781, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405818, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405852, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448405854, "dur": 173, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406032, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406034, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406077, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406082, "dur": 29, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406116, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406153, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406155, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406195, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406198, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406234, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406236, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406274, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406309, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406312, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406348, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406351, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406389, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406391, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406425, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406427, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406461, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406463, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406496, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406498, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406535, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406538, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406573, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406575, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406611, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406613, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406662, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406665, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406701, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406704, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406736, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406738, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406771, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406773, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406807, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406811, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406848, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406851, "dur": 31, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406885, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406889, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406923, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406925, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406967, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448406970, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407005, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407007, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407041, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407044, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407082, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407085, "dur": 33, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407121, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407125, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407164, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407167, "dur": 39, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407209, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407213, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407255, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407266, "dur": 147, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407418, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407454, "dur": 10, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407465, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407501, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407504, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407536, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407568, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407570, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407604, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407606, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407674, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407676, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407716, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407719, "dur": 38, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407761, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407764, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407829, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407831, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407872, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407874, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407914, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407917, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407975, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448407979, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408021, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408024, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408065, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408067, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408102, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408105, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408138, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408140, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408176, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408178, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408212, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408214, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408249, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408251, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408286, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408288, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408331, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408334, "dur": 40, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408378, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408381, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408421, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408423, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408469, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408472, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408511, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408514, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408549, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408552, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408589, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408591, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408631, "dur": 10, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408644, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408683, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408686, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408728, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408731, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408765, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408769, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408808, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408810, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408848, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408850, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408882, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408884, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408919, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408922, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408957, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408959, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408994, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448408997, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409031, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409034, "dur": 36, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409074, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409076, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409109, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409111, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409148, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409150, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409182, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409279, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409314, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409317, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409355, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448409358, "dur": 5817, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415183, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415187, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415242, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415245, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415284, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415286, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415323, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415467, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415472, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415506, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448415510, "dur": 1025, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416538, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416542, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416576, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416578, "dur": 210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416793, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416825, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416827, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416857, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416898, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416933, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416935, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448416967, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417176, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417207, "dur": 265, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417478, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417510, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417513, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417657, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417690, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417764, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417796, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417798, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417831, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417921, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417958, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448417962, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418026, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418055, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418057, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418088, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418189, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418219, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418221, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418313, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418357, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418359, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418443, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418475, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418506, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418628, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418666, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418694, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418698, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418728, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418730, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418761, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418763, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418800, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418851, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418854, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418888, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448418968, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419007, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419009, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419049, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419082, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419084, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419107, "dur": 268, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419380, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419414, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419418, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419462, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419495, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419497, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419685, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419714, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419716, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419753, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419819, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419857, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419860, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419893, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419986, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448419988, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420039, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420041, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420088, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420130, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420161, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420312, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420341, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420374, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420378, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420415, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420417, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420459, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420597, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420645, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420648, "dur": 174, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420827, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420872, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420874, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420953, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420992, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448420995, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421031, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421034, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421099, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421101, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421134, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421137, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421172, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421174, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421288, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421326, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421328, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421437, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421475, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421477, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421514, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421516, "dur": 408, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421927, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421930, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421968, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448421971, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422011, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422014, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422075, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422112, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422114, "dur": 465, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422584, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422621, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422624, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422742, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422786, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422788, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422853, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422888, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422890, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448422991, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423025, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423028, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423064, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423068, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423104, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423107, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423140, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423143, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423178, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423180, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423231, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423263, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423266, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423315, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423358, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423470, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423503, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423505, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423537, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423540, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423572, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423575, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423610, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423613, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423809, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423843, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448423972, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424009, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424012, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424049, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424052, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424087, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424089, "dur": 133, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424227, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424229, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424265, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424267, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424300, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424302, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424462, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424486, "dur": 509, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448424998, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425001, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425037, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425041, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425105, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425139, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425142, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425313, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425315, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425359, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425362, "dur": 502, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425868, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425870, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425907, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425909, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425954, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448425956, "dur": 245, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448426207, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448426242, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448426244, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448426282, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448426284, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448426433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448426435, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448426480, "dur": 764, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448427248, "dur": 42, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448427294, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448427297, "dur": 221, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448427522, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448427525, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448427559, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448427561, "dur": 445, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428012, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428049, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428051, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428218, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428264, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428302, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428342, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428344, "dur": 194, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428541, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428545, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428582, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428584, "dur": 192, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428782, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428827, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428830, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428864, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428866, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448428999, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429002, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429037, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429039, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429074, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429076, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429340, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429343, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429381, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429384, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429419, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429421, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429457, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429459, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429503, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429505, "dur": 290, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429799, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429801, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429839, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448429841, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430046, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430087, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430089, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430125, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430297, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430332, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430335, "dur": 214, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430552, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430554, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430591, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430593, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430629, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430632, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430773, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430776, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430814, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430817, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430855, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448430857, "dur": 454, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431317, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431320, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431359, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431362, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431401, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431403, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431485, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431522, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431524, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431559, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431561, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431649, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431683, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431685, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431728, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431762, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431765, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431854, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431888, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431890, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431927, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448431929, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432039, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432071, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432073, "dur": 265, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432343, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432380, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432382, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432417, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432419, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432581, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432617, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432621, "dur": 193, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432818, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432853, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448432856, "dur": 338, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433198, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433200, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433240, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433243, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433286, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433288, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433404, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433446, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433448, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433500, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433541, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433543, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433586, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433588, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433630, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433632, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433731, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433733, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433777, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433779, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433910, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433946, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448433948, "dur": 323, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434275, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434277, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434327, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434330, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434450, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434489, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434491, "dur": 250, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434746, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448434784, "dur": 678, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448435467, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448435502, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448435505, "dur": 71532, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448507046, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448507053, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448507097, "dur": 2571, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448509679, "dur": 3149, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448512836, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448512839, "dur": 190, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448513034, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448513037, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448513101, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448513103, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448513144, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448513147, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448513495, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448513529, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448513531, "dur": 1138, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448514673, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448514675, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448514728, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448514730, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448514887, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448514922, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448515062, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448515098, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448515226, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448515229, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448515262, "dur": 560, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448515827, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448515861, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448515930, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448515963, "dur": 1160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448517128, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448517160, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448517162, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448517304, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448517336, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448517339, "dur": 173, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448517517, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448517550, "dur": 542, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448518097, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448518133, "dur": 1429, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448519566, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448519568, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448519603, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448519605, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448519646, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448519684, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448519719, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448519721, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448519764, "dur": 1107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448520875, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448520877, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448520931, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448520933, "dur": 1008, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448521945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448521948, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448521989, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448521992, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522025, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522028, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522113, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522148, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522271, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522308, "dur": 379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522691, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522694, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522728, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448522730, "dur": 1506, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524240, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524242, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524280, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524282, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524434, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524473, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524475, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524679, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524713, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524714, "dur": 222, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524943, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448524979, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448525014, "dur": 1840, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448526858, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448526861, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448526897, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448526899, "dur": 191, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527094, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527096, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527130, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527132, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527215, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527246, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527248, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527283, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527471, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527505, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448527507, "dur": 1702, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529212, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529215, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529257, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529260, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529344, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529380, "dur": 394, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529778, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529780, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529830, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529832, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529875, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448529918, "dur": 1632, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448531555, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448531558, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448531606, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448531608, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448531728, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448531773, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448531775, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448531815, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448531848, "dur": 395, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532250, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532288, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532290, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532323, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532328, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532451, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532486, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532488, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448532523, "dur": 1328, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448533856, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448533890, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448533892, "dur": 370, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534268, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534304, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534603, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534637, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534820, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534868, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534870, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534911, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534945, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534947, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534984, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448534987, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535025, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535027, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535063, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535098, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535136, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535182, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535185, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535219, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535222, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535265, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535267, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535302, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535305, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535340, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535342, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535409, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535411, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535463, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535466, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535542, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535588, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535590, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535672, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535675, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535718, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535721, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535754, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535787, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535818, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535823, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535855, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535887, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535936, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535968, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448535999, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536033, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536065, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536094, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536096, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536138, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536176, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536178, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536218, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536221, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536256, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536258, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536295, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536298, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536352, "dur": 227, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536584, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536587, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536631, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536633, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536681, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536683, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536721, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536723, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536764, "dur": 1, "ph": "X", "name": "ProcessMessages 45", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536786, "dur": 38, "ph": "X", "name": "ReadAsync 45", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536827, "dur": 4, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536833, "dur": 39, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536875, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536878, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536917, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536920, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536959, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448536961, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537008, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537013, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537068, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537071, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537123, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537126, "dur": 40, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537170, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537172, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537206, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537208, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537330, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537332, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537375, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537378, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537420, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537422, "dur": 127, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537553, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448537555, "dur": 154370, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448691945, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448691949, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448691986, "dur": 18, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448692005, "dur": 2216, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448694228, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448694232, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448694313, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448694315, "dur": 9093, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448703419, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448703423, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448703481, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448703485, "dur": 108, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448703599, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448703643, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448703646, "dur": 103612, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448807268, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448807273, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448807328, "dur": 50, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448807383, "dur": 9801, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448817194, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448817201, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448817272, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448817274, "dur": 3120, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448820415, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448820418, "dur": 133, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448820556, "dur": 34, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448820592, "dur": 9019, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448829622, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448829626, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448829666, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448829670, "dur": 2537, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448832242, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448832249, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448832388, "dur": 67, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448832460, "dur": 35558, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448868029, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448868033, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448868091, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448868095, "dur": 1535, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448869637, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448869640, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448869697, "dur": 29, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448869728, "dur": 573, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448870305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448870308, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448870355, "dur": 363, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751401448870722, "dur": 13318, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5212, "tid": 128, "ts": 1751401448903966, "dur": 3519, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5212, "tid": 8589934592, "ts": 1751401448354261, "dur": 138698, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751401448492962, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751401448492972, "dur": 1541, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5212, "tid": 128, "ts": 1751401448907488, "dur": 19, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5212, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401448331765, "dur": 553996, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401448335709, "dur": 10152, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401448886129, "dur": 6994, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401448890594, "dur": 153, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751401448893264, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5212, "tid": 128, "ts": 1751401448907509, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751401448364897, "dur": 2482, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401448367393, "dur": 1019, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401448368568, "dur": 107, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751401448368675, "dur": 381, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401448370511, "dur": 2052, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401448373922, "dur": 2258, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401448376244, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751401448376906, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751401448377181, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401448377386, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751401448377549, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751401448377691, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_B7135FF78EF23DD9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401448377825, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751401448377969, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401448379971, "dur": 188, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401448386543, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751401448388172, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401448388360, "dur": 154, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751401448389912, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751401448396469, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751401448396663, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751401448397956, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.FilmInternalUtilities.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751401448369088, "dur": 34594, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401448403698, "dur": 466601, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401448870301, "dur": 228, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401448870647, "dur": 55, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401448870766, "dur": 80, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401448870889, "dur": 2318, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751401448369380, "dur": 34331, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448403738, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448403966, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401448404440, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448404793, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448404947, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401448405391, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401448405633, "dur": 514, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401448406264, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401448407084, "dur": 392, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401448407546, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448407762, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401448407842, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448408033, "dur": 583, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751401448408666, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401448409091, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401448409406, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751401448409716, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448409912, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448410144, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448410355, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448410541, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448410689, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448410896, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448411131, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448411372, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448411582, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448411798, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448412016, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448412230, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448412439, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448413131, "dur": 2308, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\MipmapStreamingShaderProperties.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751401448413029, "dur": 2530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448415560, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448415997, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448416671, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448416902, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448417117, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448417581, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401448417783, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448418370, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448419096, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401448419308, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401448419574, "dur": 5985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448425638, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448425710, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401448425923, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448426532, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448426761, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448427031, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448427268, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448427502, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401448427663, "dur": 1101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448428765, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448428919, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448429194, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751401448429381, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448429985, "dur": 2999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448432984, "dur": 1933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448434917, "dur": 77757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448512676, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448515262, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448515336, "dur": 2353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448517733, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448520161, "dur": 2323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448522533, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448524843, "dur": 2581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448527481, "dur": 2426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448529951, "dur": 2339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448532323, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448532382, "dur": 2441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448534870, "dur": 2317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751401448537188, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448537744, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751401448537983, "dur": 332298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448369447, "dur": 34279, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448403738, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448403955, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401448404329, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_13694A2D45790405.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401448404444, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448404588, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448404788, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A44562DC2280D3AD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401448405568, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751401448405776, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448406114, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_7C46D60DEB68F39C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401448406402, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751401448406455, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448406654, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751401448406843, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751401448407448, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751401448407820, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448408011, "dur": 934, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751401448409671, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448410321, "dur": 619, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751401448409760, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448410941, "dur": 1089, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\Analytics\\MultiplayerCenterAnalyticsFactory.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751401448410941, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448412425, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448413017, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448413250, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448413947, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448414193, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448414405, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448414623, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448414832, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448415037, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448415284, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448415516, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448415749, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448415973, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448416250, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448416501, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448416723, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448416953, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448417198, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401448417389, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401448417507, "dur": 2744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448420329, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401448420586, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401448420640, "dur": 2779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448423492, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751401448423771, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448424418, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1751401448425383, "dur": 147, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448427078, "dur": 80560, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1751401448511419, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448513699, "dur": 2089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448515834, "dur": 2236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448518123, "dur": 2172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448520341, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448522718, "dur": 2271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448525038, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448527459, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448529685, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448529759, "dur": 2276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448532036, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448532152, "dur": 2253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448534460, "dur": 2245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751401448536914, "dur": 328, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751401448537771, "dur": 279474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751401448817320, "dur": 358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751401448817250, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751401448817731, "dur": 52545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448369491, "dur": 34254, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448403752, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448403860, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FAA2D6271BC6A85C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448404436, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_611E434C56DD2756.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448404876, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BD82F057A3253721.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448405128, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448405587, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401448405908, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401448406187, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401448406283, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401448406380, "dur": 529, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401448407197, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751401448407308, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751401448407740, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448407815, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448408319, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751401448408932, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751401448409711, "dur": 619, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance\\Editor\\TestRunBuilder.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751401448409711, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448410546, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448410754, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448410979, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448411243, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448411461, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448411666, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448411872, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448412109, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448412296, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448412481, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448413139, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\GraphConcretization.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751401448413062, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448413977, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448414281, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448414482, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448414781, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448415072, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448415293, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448415504, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448415721, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448415939, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448416227, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448416455, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448416691, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448416925, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448417138, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448417385, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448417533, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448419082, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448419234, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448419459, "dur": 1418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448420952, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448421193, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448421251, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448421430, "dur": 1072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448422528, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448423198, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448424177, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_9B5591808ABA37AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448424677, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448424922, "dur": 141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448425064, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448425348, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448425617, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448426010, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448426225, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448426438, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448426663, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448426893, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448427207, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448427662, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448428879, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448429589, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448429758, "dur": 633, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448430404, "dur": 1607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448432013, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448432112, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448432337, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448433216, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448433437, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448434114, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751401448434218, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448434523, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448434913, "dur": 63772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448498686, "dur": 12743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448511431, "dur": 2621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448514102, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448516536, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448518786, "dur": 9076, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448527870, "dur": 4494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448532425, "dur": 5486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751401448537912, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751401448537999, "dur": 332297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448369537, "dur": 34224, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448403767, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448403963, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448404336, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448404467, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448405007, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_9A715A3879D71476.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448405286, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401448405496, "dur": 851, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401448406570, "dur": 588, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751401448407317, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401448407453, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751401448407829, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448408081, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401448408342, "dur": 342, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751401448409720, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448409957, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448410167, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448410373, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448410567, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448410799, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448411027, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448411267, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448411501, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448411714, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448411922, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448412181, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448412394, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448413139, "dur": 952, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\UVMaterialSlot.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751401448412980, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448414134, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448414342, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448414543, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448414753, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448414962, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448415183, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448415395, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448415611, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448415899, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448416231, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448416460, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448416696, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448416930, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448417238, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448417451, "dur": 1774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448419313, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448419565, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448420470, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448420626, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448420748, "dur": 6015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448426857, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448427050, "dur": 1856, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448428940, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448429310, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448429394, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448429621, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448430703, "dur": 1599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448432303, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448432642, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448433221, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448433422, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448434020, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448434904, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751401448435077, "dur": 76340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448511419, "dur": 2659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448514132, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448516432, "dur": 2223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448518697, "dur": 4545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448523295, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448525613, "dur": 2417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448528077, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448530482, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448532780, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751401448532850, "dur": 2305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448535207, "dur": 2681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751401448538020, "dur": 332253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448369636, "dur": 34141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448403785, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_1AA5CC13572AAA47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448404388, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_F0DA661B5DCD99FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448405610, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448405771, "dur": 3797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448409675, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448409883, "dur": 5826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448415821, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448416069, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448417190, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448417398, "dur": 1187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448418689, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448418911, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448418965, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448419696, "dur": 1258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448420955, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448421035, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448421242, "dur": 313, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448421557, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448421752, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448421896, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448422109, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448422678, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448423340, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448423396, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448423597, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448423840, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448424512, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448424679, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448425175, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448425418, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448425847, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448426150, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448426357, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448426568, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448426789, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448427053, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448427275, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448427498, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448427686, "dur": 1688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448429453, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448429657, "dur": 1468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448431201, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448431373, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448431969, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448432297, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448432520, "dur": 1615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448434190, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448434328, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448434903, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751401448435043, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448435346, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448436971, "dur": 255538, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448694534, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751401448694098, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448694814, "dur": 6027, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751401448694812, "dur": 7363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448703913, "dur": 250, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751401448704919, "dur": 102934, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751401448817250, "dur": 435, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751401448817208, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751401448817737, "dur": 52537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448369736, "dur": 34053, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448403795, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CCD605E26FC2B53B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448403960, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448404319, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2D31BD0D6ECE4F8B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448404442, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448405667, "dur": 491, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751401448406275, "dur": 299, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751401448406610, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751401448407819, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448408310, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751401448409712, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751401448409924, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448410128, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448410332, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448410535, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448410724, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448410920, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448411211, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448411437, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448411667, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448411875, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448412131, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448412345, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448412929, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448413145, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448413505, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448413781, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448414006, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448414226, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448414428, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448414634, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448414848, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448415076, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448415497, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448415713, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448415930, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448416195, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448416423, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448416669, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448416884, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448417099, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448417359, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448417538, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448418080, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448418260, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448418420, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448419380, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448419656, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448420418, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448420610, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448420740, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448421711, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448421770, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448422037, "dur": 1284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448423358, "dur": 279, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448423640, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448423849, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448423913, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448423965, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448424526, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448424665, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751401448424884, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448424988, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448425261, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448425557, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448426075, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448426302, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448426645, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448426922, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448427267, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448427500, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448427682, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448428620, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448428885, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448429192, "dur": 2235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448431451, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448431942, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448432125, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448433001, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751401448433179, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448433819, "dur": 1089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448434908, "dur": 60196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448496494, "dur": 440, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1751401448496935, "dur": 1503, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1751401448498439, "dur": 224, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1751401448495105, "dur": 3567, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448498673, "dur": 12749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448511424, "dur": 4022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448515493, "dur": 2343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448517837, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448517911, "dur": 2352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448520307, "dur": 2518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448522875, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448525284, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448527700, "dur": 5348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448533092, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448535425, "dur": 842, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751401448536911, "dur": 334, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751401448537282, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751401448537488, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448537638, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751401448537741, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751401448537965, "dur": 332337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448369784, "dur": 34018, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448403960, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448404333, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A7F411FD3CF1CCBD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448404420, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448405606, "dur": 901, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751401448406629, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448406744, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751401448407129, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751401448407789, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751401448407850, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448408338, "dur": 432, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751401448408908, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751401448409249, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751401448409696, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448409928, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448410155, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448410545, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448410754, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448410979, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448411235, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448411462, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448411665, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448411873, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448412119, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448412323, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448412532, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448413135, "dur": 1799, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\BooleanMaterialSlot.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751401448413107, "dur": 1999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448415107, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448415422, "dur": 624, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\GPUDriven\\OccluderDepthPyramidConstants.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751401448415399, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448416290, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448416536, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448416751, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448416979, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448417199, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448417396, "dur": 9891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448427365, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448428121, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448429189, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448429375, "dur": 1468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448430924, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448431153, "dur": 1060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448432286, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448432461, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448432972, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448433216, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448433418, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448433844, "dur": 1061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448434907, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751401448435086, "dur": 83909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448518997, "dur": 2365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448521420, "dur": 4056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448525538, "dur": 6213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448531752, "dur": 1290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448533055, "dur": 2449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448535797, "dur": 1108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448536905, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Runtime.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751401448537560, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751401448537758, "dur": 156346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448694127, "dur": 6717, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751401448694105, "dur": 8060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448703457, "dur": 225, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751401448704690, "dur": 116295, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751401448829780, "dur": 38737, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751401448829689, "dur": 38830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751401448868540, "dur": 1685, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751401448369840, "dur": 33993, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448403956, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448404322, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401448404448, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448404801, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_6946749C828DF1C9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401448405562, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751401448405697, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401448405921, "dur": 2984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448408938, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448409823, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448410084, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448410336, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448410907, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448411139, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448411389, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448411603, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448411829, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448412220, "dur": 924, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Util\\SlotValueTypeUtil.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751401448412053, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448413209, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448413423, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448413670, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448414090, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448414293, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448414497, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448414727, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448414932, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448415139, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448415447, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448415654, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448415917, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448416466, "dur": 2031, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Expression.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751401448416244, "dur": 2254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448418544, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401448418823, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401448419052, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448419989, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448420072, "dur": 1070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448421143, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448421632, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401448421924, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448422589, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448423191, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448423718, "dur": 1070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448424853, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401448425035, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448426490, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448427061, "dur": 983, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnBecameVisibleMessageListener.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751401448426884, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448428878, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448429203, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751401448429419, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448429976, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448430091, "dur": 2887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448432979, "dur": 1939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448434918, "dur": 76502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448511429, "dur": 1927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448513414, "dur": 2211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448515670, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448517934, "dur": 2276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448520253, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448522611, "dur": 2624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448525241, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448525302, "dur": 2392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448527695, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448527821, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448530299, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448530370, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448530436, "dur": 2411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448532848, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448532917, "dur": 2466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448535414, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751401448535613, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751401448535941, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448536002, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751401448536134, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751401448536241, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448536891, "dur": 499, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751401448537391, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751401448537474, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448537581, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448537772, "dur": 291957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751401448829767, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751401448829731, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751401448830200, "dur": 2522, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751401448832744, "dur": 37559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751401448881348, "dur": 2012, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5212, "tid": 128, "ts": 1751401448908960, "dur": 3160, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5212, "tid": 128, "ts": 1751401448912174, "dur": 3153, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5212, "tid": 128, "ts": 1751401448901146, "dur": 15222, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}