{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5212, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5212, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5212, "tid": 745, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5212, "tid": 745, "ts": 1751403371951975, "dur": 1665, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5212, "tid": 745, "ts": 1751403371961414, "dur": 1891, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5212, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5212, "tid": 1, "ts": 1751403371155689, "dur": 10336, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751403371166043, "dur": 88856, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751403371254914, "dur": 56489, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5212, "tid": 745, "ts": 1751403371963313, "dur": 29, "ph": "X", "name": "", "args": {}}, {"pid": 5212, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371152588, "dur": 20886, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371173478, "dur": 763126, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371175450, "dur": 3690, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371179150, "dur": 2048, "ph": "X", "name": "ProcessMessages 11897", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371181212, "dur": 1411, "ph": "X", "name": "ReadAsync 11897", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371182628, "dur": 20, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371182649, "dur": 190, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371182844, "dur": 9, "ph": "X", "name": "ProcessMessages 10407", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371182854, "dur": 48, "ph": "X", "name": "ReadAsync 10407", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371182905, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371182907, "dur": 38, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371182948, "dur": 36, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371182987, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371182988, "dur": 37, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183029, "dur": 31, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183063, "dur": 35, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183101, "dur": 39, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183145, "dur": 37, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183185, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183186, "dur": 42, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183231, "dur": 37, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183273, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183276, "dur": 35, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183314, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183316, "dur": 37, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183356, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183358, "dur": 47, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183408, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183410, "dur": 44, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183457, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183460, "dur": 44, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183506, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183507, "dur": 33, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183543, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183544, "dur": 34, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183582, "dur": 35, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183620, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183621, "dur": 36, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183661, "dur": 36, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183699, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183701, "dur": 35, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183739, "dur": 31, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183774, "dur": 36, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183813, "dur": 36, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183853, "dur": 34, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183891, "dur": 36, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183930, "dur": 34, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183966, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371183967, "dur": 31, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184002, "dur": 34, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184040, "dur": 35, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184079, "dur": 36, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184117, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184119, "dur": 36, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184159, "dur": 36, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184198, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184199, "dur": 35, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184238, "dur": 36, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184278, "dur": 34, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184314, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184315, "dur": 33, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184351, "dur": 33, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184388, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184428, "dur": 35, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184465, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184467, "dur": 36, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184505, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184507, "dur": 33, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184543, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184544, "dur": 31, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184577, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184580, "dur": 37, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184620, "dur": 35, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184658, "dur": 35, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184696, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184698, "dur": 35, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184736, "dur": 32, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184771, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184772, "dur": 31, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184806, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184848, "dur": 35, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184885, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184887, "dur": 39, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184929, "dur": 37, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184968, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371184970, "dur": 31, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185005, "dur": 31, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185040, "dur": 37, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185081, "dur": 35, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185118, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185120, "dur": 36, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185160, "dur": 34, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185195, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185197, "dur": 34, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185235, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185289, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185292, "dur": 58, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185353, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185356, "dur": 48, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185407, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185409, "dur": 34, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185448, "dur": 35, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185487, "dur": 34, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185525, "dur": 35, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185562, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185563, "dur": 36, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185603, "dur": 34, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185639, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185640, "dur": 36, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185680, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185723, "dur": 35, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185760, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185761, "dur": 35, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185800, "dur": 34, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185838, "dur": 32, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185874, "dur": 32, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185909, "dur": 35, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185947, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185948, "dur": 35, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371185987, "dur": 34, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186024, "dur": 32, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186058, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186060, "dur": 30, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186093, "dur": 64, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186160, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186198, "dur": 40, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186242, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186245, "dur": 46, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186294, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186297, "dur": 70, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186371, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186374, "dur": 39, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186416, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186418, "dur": 47, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186468, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186470, "dur": 40, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186513, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186515, "dur": 51, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186568, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186570, "dur": 35, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186607, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186609, "dur": 32, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186645, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186689, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186691, "dur": 40, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186734, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186736, "dur": 45, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186783, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186786, "dur": 38, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186826, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371186828, "dur": 233, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187065, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187120, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187121, "dur": 39, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187162, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187164, "dur": 38, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187205, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187207, "dur": 37, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187247, "dur": 29, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187280, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187314, "dur": 42, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187359, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187362, "dur": 45, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187410, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187412, "dur": 110, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187525, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187528, "dur": 55, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187586, "dur": 2, "ph": "X", "name": "ProcessMessages 959", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187589, "dur": 37, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187629, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187632, "dur": 49, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187684, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187688, "dur": 49, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187741, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187744, "dur": 38, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187785, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187788, "dur": 45, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187836, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187838, "dur": 48, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187897, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187949, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187952, "dur": 41, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187996, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371187999, "dur": 49, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188051, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188053, "dur": 49, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188107, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188109, "dur": 52, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188164, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188167, "dur": 47, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188217, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188219, "dur": 51, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188274, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188276, "dur": 50, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188330, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188333, "dur": 34, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188371, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188373, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188426, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188429, "dur": 50, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188482, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188485, "dur": 42, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188530, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188532, "dur": 64, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188600, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188603, "dur": 46, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188653, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188656, "dur": 53, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188711, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188714, "dur": 48, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188765, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188769, "dur": 43, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188814, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188816, "dur": 49, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188869, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188872, "dur": 52, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188927, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188930, "dur": 42, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188975, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371188977, "dur": 49, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189029, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189032, "dur": 38, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189072, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189074, "dur": 41, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189118, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189122, "dur": 50, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189175, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189177, "dur": 48, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189229, "dur": 2, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189232, "dur": 63, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189299, "dur": 2, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189303, "dur": 76, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189384, "dur": 3, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189389, "dur": 89, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189483, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189487, "dur": 72, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189564, "dur": 2, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189568, "dur": 61, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189632, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189635, "dur": 57, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189695, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189697, "dur": 58, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189759, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189762, "dur": 58, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189822, "dur": 2, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189826, "dur": 43, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189871, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189872, "dur": 30, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189905, "dur": 28, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189937, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189977, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371189978, "dur": 41, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190022, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190024, "dur": 43, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190071, "dur": 37, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190110, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190112, "dur": 33, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190148, "dur": 36, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190188, "dur": 47, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190239, "dur": 46, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190288, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190290, "dur": 52, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190346, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190349, "dur": 46, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190405, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190407, "dur": 40, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190449, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190450, "dur": 40, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190494, "dur": 35, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190531, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190533, "dur": 48, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190583, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190585, "dur": 34, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190623, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190666, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190668, "dur": 36, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190707, "dur": 37, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190746, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190748, "dur": 35, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190784, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190786, "dur": 31, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190820, "dur": 33, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190857, "dur": 31, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190892, "dur": 37, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190934, "dur": 37, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190973, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371190974, "dur": 36, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191014, "dur": 30, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191047, "dur": 36, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191087, "dur": 37, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191127, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191129, "dur": 49, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191181, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191183, "dur": 40, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191226, "dur": 2, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191229, "dur": 39, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191271, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191273, "dur": 44, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191321, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191323, "dur": 44, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191371, "dur": 38, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191411, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191413, "dur": 48, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191464, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191466, "dur": 41, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191510, "dur": 40, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191551, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191553, "dur": 37, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191594, "dur": 35, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191631, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191632, "dur": 36, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191672, "dur": 29, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191704, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191706, "dur": 53, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191761, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191763, "dur": 44, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191809, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191811, "dur": 46, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191859, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191862, "dur": 39, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191903, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191904, "dur": 36, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191943, "dur": 38, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191984, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371191985, "dur": 37, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192026, "dur": 46, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192075, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192077, "dur": 42, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192121, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192123, "dur": 32, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192158, "dur": 40, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192200, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192202, "dur": 36, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192240, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192242, "dur": 34, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192280, "dur": 32, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192314, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192315, "dur": 33, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192351, "dur": 35, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192390, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192392, "dur": 46, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192441, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192443, "dur": 41, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192486, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192488, "dur": 37, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192527, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192529, "dur": 48, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192580, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192621, "dur": 44, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192668, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192670, "dur": 46, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192718, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192720, "dur": 35, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192757, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192759, "dur": 36, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192798, "dur": 38, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192838, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192839, "dur": 37, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192880, "dur": 30, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192924, "dur": 37, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371192966, "dur": 220, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193188, "dur": 89, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193281, "dur": 5, "ph": "X", "name": "ProcessMessages 4084", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193287, "dur": 37, "ph": "X", "name": "ReadAsync 4084", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193327, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193329, "dur": 49, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193380, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193383, "dur": 48, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193433, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193435, "dur": 41, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193479, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193481, "dur": 36, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193519, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193521, "dur": 42, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193565, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193567, "dur": 40, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193610, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193612, "dur": 40, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193654, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193656, "dur": 36, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193695, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193697, "dur": 33, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193734, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193771, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193773, "dur": 41, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193817, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193819, "dur": 39, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193860, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193862, "dur": 38, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193902, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193904, "dur": 41, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193947, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193949, "dur": 33, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371193985, "dur": 41, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194029, "dur": 1, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194031, "dur": 40, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194073, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194075, "dur": 36, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194114, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194115, "dur": 35, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194155, "dur": 35, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194193, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194194, "dur": 37, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194234, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194236, "dur": 45, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194284, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194287, "dur": 48, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194338, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194340, "dur": 49, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194392, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194394, "dur": 34, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194431, "dur": 40, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194474, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194476, "dur": 39, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194518, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194519, "dur": 121, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194645, "dur": 54, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194702, "dur": 1, "ph": "X", "name": "ProcessMessages 1807", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194704, "dur": 45, "ph": "X", "name": "ReadAsync 1807", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194753, "dur": 2, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194756, "dur": 46, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194805, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194806, "dur": 37, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194846, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194848, "dur": 39, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194890, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194892, "dur": 38, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194933, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194934, "dur": 40, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194978, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371194980, "dur": 372, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371195356, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371195358, "dur": 44, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371195405, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371195407, "dur": 24, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371195435, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371195458, "dur": 21, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371195481, "dur": 762, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371196244, "dur": 39, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371196286, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371196289, "dur": 41, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371196333, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371196335, "dur": 189, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371196528, "dur": 2, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371196532, "dur": 4933, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201473, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201477, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201559, "dur": 3, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201564, "dur": 50, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201617, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201620, "dur": 56, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201680, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201683, "dur": 61, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201747, "dur": 2, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201750, "dur": 50, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201804, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201807, "dur": 50, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201859, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201862, "dur": 44, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201912, "dur": 47, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201962, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371201965, "dur": 44, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202012, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202014, "dur": 45, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202063, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202065, "dur": 63, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202132, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202135, "dur": 41, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202178, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202181, "dur": 104, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202288, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202291, "dur": 83, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202377, "dur": 2, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202381, "dur": 50, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202434, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202437, "dur": 42, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202482, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202484, "dur": 49, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202537, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202540, "dur": 40, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202584, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202587, "dur": 55, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202646, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202649, "dur": 42, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202696, "dur": 42, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202741, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202743, "dur": 42, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202787, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202790, "dur": 42, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202834, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202836, "dur": 45, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202884, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202886, "dur": 47, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202936, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202938, "dur": 34, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202974, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371202976, "dur": 42, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203020, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203023, "dur": 35, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203066, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203068, "dur": 45, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203116, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203118, "dur": 36, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203157, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203159, "dur": 40, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203202, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203204, "dur": 35, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203243, "dur": 34, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203281, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203282, "dur": 57, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203344, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203346, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203398, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203401, "dur": 39, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203441, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203443, "dur": 99, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203546, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203591, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203594, "dur": 40, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203638, "dur": 29, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203671, "dur": 78, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203754, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203756, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203804, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203806, "dur": 46, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203855, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203858, "dur": 48, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203910, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371203913, "dur": 89, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204005, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204007, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204060, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204064, "dur": 65, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204132, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204135, "dur": 41, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204180, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204182, "dur": 435, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204622, "dur": 15, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204639, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204700, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204703, "dur": 57, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204763, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204766, "dur": 40, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204810, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204812, "dur": 89, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204905, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371204907, "dur": 110, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205023, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205132, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205134, "dur": 157, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205295, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205297, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205339, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205341, "dur": 40, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205386, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205389, "dur": 255, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205649, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205651, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205714, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205718, "dur": 73, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205795, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205798, "dur": 97, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205900, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205955, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371205957, "dur": 55, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206015, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206018, "dur": 40, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206060, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206062, "dur": 95, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206161, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206163, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206225, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206228, "dur": 79, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206311, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206314, "dur": 47, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206365, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206367, "dur": 77, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206450, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206511, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206514, "dur": 52, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206568, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206571, "dur": 81, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206656, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206723, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206725, "dur": 51, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206779, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206781, "dur": 71, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206858, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206914, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206916, "dur": 67, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206987, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371206990, "dur": 68, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371207062, "dur": 3, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371207068, "dur": 432, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371207512, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371207520, "dur": 177, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371207709, "dur": 7, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371207719, "dur": 140, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371207865, "dur": 5, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371207874, "dur": 241, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208134, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208139, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208234, "dur": 2, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208238, "dur": 58, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208302, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208304, "dur": 280, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208590, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208594, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208658, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208660, "dur": 43, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208707, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208710, "dur": 99, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208812, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208814, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208859, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208863, "dur": 38, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208905, "dur": 72, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208981, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371208984, "dur": 48, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209036, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209039, "dur": 41, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209084, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209086, "dur": 63, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209153, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209155, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209214, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209217, "dur": 71, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209291, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209294, "dur": 92, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209390, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209392, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209453, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209456, "dur": 46, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209505, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209508, "dur": 66, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209577, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209579, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209630, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209633, "dur": 46, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209682, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209684, "dur": 37, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209724, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209727, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209786, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209836, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209839, "dur": 46, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209887, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209890, "dur": 73, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371209968, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210022, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210025, "dur": 31, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210058, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210060, "dur": 38, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210101, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210103, "dur": 41, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210147, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210149, "dur": 58, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210212, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210262, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210265, "dur": 51, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210319, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210322, "dur": 48, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210374, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210376, "dur": 44, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210423, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210426, "dur": 43, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210472, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210474, "dur": 40, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210517, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210519, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210590, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210641, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210644, "dur": 46, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210693, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210695, "dur": 46, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210747, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210796, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210799, "dur": 86, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210889, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210891, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210945, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210947, "dur": 45, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210996, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371210999, "dur": 70, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211075, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211126, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211128, "dur": 45, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211176, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211180, "dur": 120, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211305, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211354, "dur": 3, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211358, "dur": 48, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211409, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211412, "dur": 49, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211464, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211466, "dur": 49, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211519, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211522, "dur": 43, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211568, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211570, "dur": 41, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211615, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211617, "dur": 61, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211683, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211736, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211739, "dur": 45, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211786, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211789, "dur": 73, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211865, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211868, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211921, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211923, "dur": 42, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211968, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371211970, "dur": 41, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212015, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212017, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212091, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212136, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212138, "dur": 41, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212182, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212184, "dur": 41, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212228, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212231, "dur": 35, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212270, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212273, "dur": 56, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212332, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212334, "dur": 41, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212378, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212380, "dur": 37, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212419, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212421, "dur": 217, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212642, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212644, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212696, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212699, "dur": 80, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212784, "dur": 58, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212848, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212850, "dur": 44, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212898, "dur": 1, "ph": "X", "name": "ProcessMessages 55", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212900, "dur": 70, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212974, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371212977, "dur": 43, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213024, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213027, "dur": 38, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213070, "dur": 1, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213072, "dur": 277, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213353, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213355, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213405, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213409, "dur": 157, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213571, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213623, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213626, "dur": 53, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213684, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213687, "dur": 42, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213732, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213734, "dur": 135, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213874, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213931, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213934, "dur": 53, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213991, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371213994, "dur": 41, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214037, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214040, "dur": 91, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214135, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214137, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214194, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214196, "dur": 85, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214285, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214289, "dur": 42, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214334, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214337, "dur": 71, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214413, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214416, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214505, "dur": 2, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214511, "dur": 100, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214618, "dur": 3, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214622, "dur": 82, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214710, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214713, "dur": 86, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214805, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371214809, "dur": 195, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215010, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215014, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215085, "dur": 2, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215088, "dur": 43, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215134, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215136, "dur": 75, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215214, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215216, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215269, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215271, "dur": 46, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215321, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215324, "dur": 74, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215401, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215403, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215462, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215465, "dur": 45, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215513, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215515, "dur": 79, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215598, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215600, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215648, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215651, "dur": 45, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215699, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215701, "dur": 71, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215785, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215838, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215840, "dur": 42, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215886, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215889, "dur": 76, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371215969, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216022, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216024, "dur": 44, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216072, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216074, "dur": 77, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216156, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216207, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216210, "dur": 50, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216262, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216265, "dur": 83, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216351, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216353, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216406, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216408, "dur": 49, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216462, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216465, "dur": 45, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216513, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216516, "dur": 68, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216589, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216640, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216643, "dur": 46, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216692, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216695, "dur": 75, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216775, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216825, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216827, "dur": 46, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216876, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216879, "dur": 74, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371216958, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217010, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217013, "dur": 46, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217061, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217064, "dur": 86, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217155, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217205, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217208, "dur": 41, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217252, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217255, "dur": 79, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217340, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217394, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217396, "dur": 44, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217443, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217446, "dur": 71, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217523, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217575, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217577, "dur": 44, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217625, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217628, "dur": 73, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217706, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217757, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217759, "dur": 46, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217808, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217811, "dur": 93, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217909, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217960, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371217963, "dur": 46, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218012, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218015, "dur": 72, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218091, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218147, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218150, "dur": 45, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218198, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218201, "dur": 93, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218299, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218301, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218357, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218360, "dur": 47, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218411, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218414, "dur": 79, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218498, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218549, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218552, "dur": 44, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218599, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218601, "dur": 41, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218645, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218648, "dur": 67, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218721, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218773, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218776, "dur": 53, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218832, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218835, "dur": 37, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218875, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218877, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371218944, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219000, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219003, "dur": 34, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219038, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219040, "dur": 92, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219136, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219178, "dur": 67, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219248, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219249, "dur": 41, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219293, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219296, "dur": 63, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219364, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219410, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219412, "dur": 32, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219447, "dur": 31, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219481, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219561, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219599, "dur": 33, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219634, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219636, "dur": 34, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219672, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219673, "dur": 34, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219712, "dur": 41, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219756, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219758, "dur": 36, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219796, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219798, "dur": 27, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219830, "dur": 30, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219865, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219942, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371219980, "dur": 36, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220018, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220020, "dur": 32, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220056, "dur": 31, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220088, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220090, "dur": 33, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220125, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220127, "dur": 44, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220174, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220176, "dur": 40, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220218, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220220, "dur": 31, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220254, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220333, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220372, "dur": 31, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220405, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220407, "dur": 35, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220446, "dur": 33, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220481, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220483, "dur": 34, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220520, "dur": 33, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220557, "dur": 30, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220592, "dur": 31, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220626, "dur": 83, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220712, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220763, "dur": 115, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220882, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220885, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371220967, "dur": 447, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221419, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221478, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221483, "dur": 59, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221546, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221549, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221619, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221662, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221664, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221704, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221708, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221750, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221753, "dur": 54, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221811, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221854, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221857, "dur": 37, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221898, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221900, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221925, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221928, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221961, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371221964, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222004, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222007, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222045, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222048, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222091, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222094, "dur": 35, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222133, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222136, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222174, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222178, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222215, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222218, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222278, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222281, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222318, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222321, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222360, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222362, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222402, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222404, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222449, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222501, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222537, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222540, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222577, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222580, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222621, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222624, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222666, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222669, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222714, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222716, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222754, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222757, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222813, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222816, "dur": 61, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222883, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222886, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222954, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371222957, "dur": 56, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223018, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223022, "dur": 62, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223089, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223095, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223153, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223157, "dur": 128, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223293, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223296, "dur": 81, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223382, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223387, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223433, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223436, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223475, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223478, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223533, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223535, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223572, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223576, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223613, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223615, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223651, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223653, "dur": 45, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223701, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223705, "dur": 33, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223750, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223753, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223790, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223792, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223829, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223832, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223870, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223873, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223914, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223917, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223954, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371223957, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224003, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224005, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224051, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224054, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224090, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224093, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224132, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224135, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224171, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224174, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224244, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224248, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224295, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224299, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224336, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224339, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224388, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224391, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224428, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224431, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224480, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224484, "dur": 36, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224523, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224526, "dur": 32, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224562, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224566, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224608, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224611, "dur": 37, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224651, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224655, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224691, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224694, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224749, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224752, "dur": 34, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224790, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224792, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224831, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224835, "dur": 47, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224885, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224889, "dur": 34, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224925, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224928, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224966, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371224969, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225012, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225015, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225076, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225079, "dur": 50, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225134, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225137, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225174, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225177, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225214, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225217, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225256, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225260, "dur": 36, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225299, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225302, "dur": 34, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225340, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225343, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225384, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225386, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225426, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225574, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225612, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225617, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225653, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225658, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225693, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225696, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225750, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225753, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225791, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225794, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225827, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225830, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225866, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225868, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225906, "dur": 10, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225918, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225960, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225963, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371225999, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226002, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226036, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226039, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226075, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226077, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226110, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226114, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226148, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226150, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226184, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226187, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226224, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226227, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226276, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226279, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226322, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226324, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226370, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226373, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226414, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226417, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226457, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226461, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226502, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226505, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226544, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226547, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226615, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226618, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226664, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226666, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226705, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226708, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226747, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226751, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226790, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226792, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226831, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226833, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226870, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226872, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226912, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226914, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226952, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371226955, "dur": 64, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371227025, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371227072, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371227074, "dur": 416, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371227494, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371227496, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371227540, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371227543, "dur": 423, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371227970, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371227972, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371228022, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371228025, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371228068, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371228071, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371228102, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371228104, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371228219, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371228253, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371228255, "dur": 7808, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236071, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236075, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236129, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236133, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236231, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236233, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236276, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236278, "dur": 187, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236472, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236520, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371236523, "dur": 2562, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239094, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239099, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239156, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239160, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239200, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239204, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239373, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239418, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239420, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239458, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239461, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239496, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239498, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239531, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239533, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239648, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239680, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239683, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239874, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239905, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371239907, "dur": 822, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371240742, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371240750, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371240849, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371240854, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371240930, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371240933, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241102, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241106, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241177, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241180, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241243, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241247, "dur": 274, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241529, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241532, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241617, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241622, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241900, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241903, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241978, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371241983, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242042, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242047, "dur": 239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242292, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242295, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242338, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242342, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242381, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242384, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242425, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242430, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242643, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242645, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242694, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242697, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242739, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242758, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242815, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242850, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242853, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242886, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242889, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371242970, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243005, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243008, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243084, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243119, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243121, "dur": 367, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243492, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243494, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243531, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243534, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243655, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243657, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243693, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243696, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243749, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243751, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243786, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243788, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243831, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243833, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243873, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243875, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243942, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243977, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371243979, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244018, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244020, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244055, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244058, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244141, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244175, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244178, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244271, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244319, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244322, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244361, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244363, "dur": 420, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244788, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244790, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244830, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244833, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244871, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244874, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244908, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244910, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371244971, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245008, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245011, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245049, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245051, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245086, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245088, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245197, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245275, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245277, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245481, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245483, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245535, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245538, "dur": 263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245804, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245807, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245847, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371245849, "dur": 431, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246286, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246289, "dur": 200, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246494, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246497, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246544, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246547, "dur": 340, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246895, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246940, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371246943, "dur": 232, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247179, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247182, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247223, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247225, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247412, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247415, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247457, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247459, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247494, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247497, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247536, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247539, "dur": 252, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247798, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371247829, "dur": 776, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371248610, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371248612, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371248638, "dur": 795, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249438, "dur": 57, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249499, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249502, "dur": 245, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249751, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249753, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249802, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249805, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249883, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249887, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249922, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371249924, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250040, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250077, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250080, "dur": 202, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250287, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250326, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250329, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250393, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250426, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250429, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250534, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250536, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250570, "dur": 9, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250581, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250643, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250646, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250693, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250697, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250755, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250758, "dur": 37, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250798, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250801, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250836, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250839, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250879, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250882, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250920, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250923, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250961, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250964, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371250999, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251002, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251040, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251042, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251122, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251159, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251162, "dur": 469, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251635, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251638, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251675, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251678, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251755, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251759, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251792, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251794, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251953, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371251999, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252001, "dur": 182, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252193, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252195, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252234, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252237, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252393, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252395, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252435, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252437, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252481, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252484, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252606, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252646, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252649, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252684, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252686, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252765, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252768, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252802, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252804, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252879, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252881, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252924, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252926, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252970, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371252972, "dur": 466, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253445, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253490, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253493, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253534, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253537, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253571, "dur": 149, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253724, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253728, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253787, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253789, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253835, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253838, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253878, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371253881, "dur": 887, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371254775, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371254779, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371254828, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371254831, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371254954, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371254956, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371254997, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255000, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255042, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255044, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255086, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255089, "dur": 292, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255388, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255450, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255452, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255558, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255561, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255616, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255618, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255741, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255743, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255781, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255784, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255819, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371255821, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256027, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256071, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256074, "dur": 119, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256198, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256231, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256234, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256385, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256424, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256426, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256480, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256514, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256516, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256600, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256634, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256637, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256692, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371256696, "dur": 524, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371257225, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371257229, "dur": 441, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371257686, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371257695, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371257837, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371257844, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371257959, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371257964, "dur": 264, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371258238, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371258242, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371258285, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371258288, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371258368, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371258372, "dur": 715, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259095, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259099, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259157, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259160, "dur": 165, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259330, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259333, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259395, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259398, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259570, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259623, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259625, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259707, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259766, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259768, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259805, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371259807, "dur": 231, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260044, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260080, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260082, "dur": 264, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260350, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260352, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260417, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260419, "dur": 151, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260575, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260609, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260661, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260664, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260695, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260698, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260841, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260873, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260875, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260904, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371260985, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261018, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261020, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261094, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261125, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261127, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261167, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261169, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261207, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261209, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261290, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261328, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261330, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261377, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261381, "dur": 513, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261898, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261900, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261952, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261954, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261994, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371261997, "dur": 139, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371262139, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371262141, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371262180, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371262182, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371262265, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371262269, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371262313, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371262316, "dur": 1234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371263559, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371263564, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371263624, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371263628, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371263795, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371263798, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371263839, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371263842, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371264051, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371264053, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371264099, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371264102, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371264131, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371264219, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371264221, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371264325, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371264327, "dur": 823, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371265159, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371265165, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371265246, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371265250, "dur": 184, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371265440, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371265479, "dur": 613, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266097, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266100, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266141, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266144, "dur": 191, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266340, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266386, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266388, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266455, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266457, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266506, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266508, "dur": 446, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371266958, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371267002, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371267004, "dur": 1185, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371268195, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371268198, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371268247, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371268251, "dur": 118750, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371387012, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371387017, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371387085, "dur": 2086, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371389178, "dur": 3506, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371392692, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371392696, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371392745, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371392747, "dur": 214, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371392966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371392968, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393010, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393012, "dur": 71, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393088, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393090, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393125, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393127, "dur": 207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393339, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393341, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393378, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393381, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393454, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393456, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393490, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371393493, "dur": 1535, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395034, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395076, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395078, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395313, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395325, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395363, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395365, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395473, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395510, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395514, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395555, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395586, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395588, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395680, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395712, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395713, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395813, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395883, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371395887, "dur": 378, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371396269, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371396272, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371396311, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371396314, "dur": 1293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371397616, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371397620, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371397678, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371397681, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371397804, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371397806, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371397849, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371397851, "dur": 304, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371398160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371398164, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371398203, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371398205, "dur": 680, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371398891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371398894, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371398933, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371398936, "dur": 886, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371399848, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371399859, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371399983, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371399988, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400052, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400055, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400240, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400243, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400302, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400304, "dur": 472, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400794, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400797, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400845, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400850, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400920, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400955, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371400958, "dur": 604, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371401566, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371401569, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371401604, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371401607, "dur": 548, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402158, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402161, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402207, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402209, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402450, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402453, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402489, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402492, "dur": 286, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402781, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402784, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402818, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371402821, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371403117, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371403119, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371403154, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371403157, "dur": 485, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371403646, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371403648, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371403688, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371403691, "dur": 400, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371404094, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371404096, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371404131, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371404133, "dur": 678, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371404817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371404820, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371404864, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371404868, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405146, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405148, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405194, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405288, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405291, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405334, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405336, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405673, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405718, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371405720, "dur": 904, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371406629, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371406632, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371406674, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371406677, "dur": 428, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407109, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407111, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407171, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407174, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407261, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407265, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407336, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407340, "dur": 272, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407619, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407623, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407742, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371407763, "dur": 1814, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371409586, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371409591, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371409673, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371409676, "dur": 301, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371409982, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371409986, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410034, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410036, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410088, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410091, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410131, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410133, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410171, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410173, "dur": 503, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410683, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410740, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410742, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410973, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371410975, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371411011, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371411014, "dur": 1361, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371412379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371412382, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371412426, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371412429, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371412524, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371412526, "dur": 445, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371412977, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371412979, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413022, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413027, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413176, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413179, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413263, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413266, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413615, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413617, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413659, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413662, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413733, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413735, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413764, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413766, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413806, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413840, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413842, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413879, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371413881, "dur": 604, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414494, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414497, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414541, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414544, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414583, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414585, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414626, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414630, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414672, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414675, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414713, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414716, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414757, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414760, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414806, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414808, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414855, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414859, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414903, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414906, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414959, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371414962, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415004, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415007, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415047, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415050, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415090, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415093, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415132, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415135, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415177, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415180, "dur": 39, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415224, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415226, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415276, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415280, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415322, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415326, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415368, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415371, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415461, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415468, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415549, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415553, "dur": 87, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415647, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415651, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415728, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415732, "dur": 80, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415818, "dur": 2, "ph": "X", "name": "ProcessMessages 29", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415886, "dur": 101, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371415992, "dur": 10, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416004, "dur": 73, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416084, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416089, "dur": 74, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416173, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416177, "dur": 72, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416255, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416258, "dur": 62, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416340, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416345, "dur": 63, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416414, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416418, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416472, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416476, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416533, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416538, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416572, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416574, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416637, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416640, "dur": 90, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416753, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371416756, "dur": 651, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371417414, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371417420, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371417479, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371417482, "dur": 99781, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371517273, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371517277, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371517333, "dur": 32, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371517367, "dur": 24288, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371541666, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371541670, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371541745, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371541748, "dur": 48488, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371590247, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371590251, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371590308, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371590313, "dur": 152608, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371742947, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371742956, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371743076, "dur": 37, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371743115, "dur": 9300, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371752425, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371752432, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371752468, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371752473, "dur": 2716, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371755201, "dur": 63, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371755266, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371755341, "dur": 38, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371755381, "dur": 38056, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371793447, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371793462, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371793523, "dur": 30, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371793555, "dur": 24312, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371817878, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371817881, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371817919, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371817923, "dur": 1294, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371819225, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371819229, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371819288, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371819293, "dur": 1953, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371821256, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371821261, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371821320, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371821322, "dur": 42, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371821366, "dur": 26, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371821395, "dur": 93472, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371914876, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371914879, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371914940, "dur": 43, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371914985, "dur": 7263, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371922258, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371922262, "dur": 144, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371922410, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371922415, "dur": 2357, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371924782, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371924786, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371924852, "dur": 44, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371924898, "dur": 742, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371925647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371925650, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371925713, "dur": 321, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403371926037, "dur": 10287, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5212, "tid": 745, "ts": 1751403371963345, "dur": 5029, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5212, "tid": 8589934592, "ts": 1751403371147960, "dur": 163485, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751403371311449, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751403371311457, "dur": 1703, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5212, "tid": 745, "ts": 1751403371968379, "dur": 12, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5212, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403371116233, "dur": 821837, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403371120832, "dur": 13820, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403371938346, "dur": 8497, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403371943757, "dur": 217, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403371946984, "dur": 27, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5212, "tid": 745, "ts": 1751403371968401, "dur": 21, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751403371169120, "dur": 3781, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371172920, "dur": 2086, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371175342, "dur": 117, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751403371175460, "dur": 733, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371177341, "dur": 219, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_5DB2248094ADF2F7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403371178778, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_40A52831373DC9B0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403371179276, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FAA2D6271BC6A85C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403371179348, "dur": 2026, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_AAC2C267FBE7A1FE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403371182252, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403371185795, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371187028, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751403371187119, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751403371187177, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403371187769, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371188018, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_35E0707C8501A09D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403371188807, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751403371188893, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371188982, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403371192684, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371194481, "dur": 316, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371194954, "dur": 779, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751403371201770, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403371201846, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371204041, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751403371204125, "dur": 233, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403371204446, "dur": 397, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371205660, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403371206858, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751403371206969, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403371207033, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371207643, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371208084, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403371208326, "dur": 130, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751403371210089, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403371212019, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751403371212075, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751403371212141, "dur": 297, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403371212472, "dur": 358, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17242669345997350737.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371213115, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403371213913, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751403371214104, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403371214490, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403371218658, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403371176248, "dur": 43952, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371220216, "dur": 704165, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371924391, "dur": 129, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371924521, "dur": 108, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371924635, "dur": 55, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371924768, "dur": 92, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371924957, "dur": 110, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371925122, "dur": 1775, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751403371176359, "dur": 43875, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371220279, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371220348, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751403371220442, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_1974DA72EEA9484D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371221140, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371221247, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_0D411EBC79DB1BBD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371221839, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371223007, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_2A790542FCF7EA37.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371223074, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_2A790542FCF7EA37.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371223208, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371223387, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751403371223761, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751403371223846, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751403371224072, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751403371224172, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751403371224358, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751403371224832, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371225209, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751403371225789, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751403371226057, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371226227, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371226384, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371226705, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371227535, "dur": 4608, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751403371226957, "dur": 5218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371232176, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371232436, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371232663, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371232930, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371233333, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371233559, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371233809, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371234517, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371234737, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371234978, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371235197, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371235447, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371235706, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371236031, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371236383, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371236713, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371237027, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Pooling\\ListPool.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751403371237834, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Pooling\\ArrayPool.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751403371236966, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371238613, "dur": 1331, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Window\\TimelineWindow_PreviewPlayMode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751403371238478, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371240018, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371240181, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371240307, "dur": 1088, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371241400, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371242219, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371242342, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371242548, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371243413, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371244171, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371244279, "dur": 8356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371252636, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371253007, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371253191, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371253691, "dur": 1576, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371255276, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371255464, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371256112, "dur": 4497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371260611, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403371260824, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371261459, "dur": 4144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371265603, "dur": 47930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371315103, "dur": 223, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1751403371315327, "dur": 1455, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1751403371316783, "dur": 178, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1751403371313535, "dur": 3435, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371316970, "dur": 72968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371389940, "dur": 2820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371392809, "dur": 2840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371395650, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371395737, "dur": 2560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371398358, "dur": 3037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371401397, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371401620, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371401685, "dur": 3404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371405141, "dur": 3737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371408880, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371408942, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371409141, "dur": 2699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371411841, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371411918, "dur": 3807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403371415727, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371415916, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403371416045, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751403371416108, "dur": 508215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371176407, "dur": 43861, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371220293, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371220727, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371220912, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_7D12CF9E98D9D784.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371221784, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0D58F8E6C2E92DF4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371221993, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0D58F8E6C2E92DF4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371222529, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751403371222859, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751403371223385, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751403371223749, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751403371223816, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751403371224113, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371224205, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751403371224587, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751403371224839, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371225376, "dur": 524, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751403371225901, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12853064938409020984.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751403371226072, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371226228, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371226376, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751403371226480, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751403371226545, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371226758, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371227021, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371227371, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371227718, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371227932, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371228148, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371228396, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371228630, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371228958, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371229156, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371229369, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371229606, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371229850, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371230081, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371230399, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371230758, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371231023, "dur": 667, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\Texture2DShaderProperty.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751403371231023, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371231922, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371232145, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371232410, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371232634, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371232850, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371233066, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371233299, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371233531, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371233757, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371233992, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371234237, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371234460, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371234666, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371234889, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371235140, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371235383, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371236110, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371236360, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371236592, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371236819, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371237309, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371237516, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371237751, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371238027, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371238321, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371238691, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371239137, "dur": 2573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371241713, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371241884, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371242575, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371243128, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371243482, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371244350, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371244511, "dur": 1193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371245727, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371245973, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371246326, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371246682, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371246856, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371246938, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371247109, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371247267, "dur": 1005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371248321, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371248871, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371249358, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371250048, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371250172, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371250436, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371250506, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371250904, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371251339, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371251583, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371251829, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371252155, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371252348, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371253178, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371253324, "dur": 1203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371254529, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371254755, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371254878, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371255378, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371255491, "dur": 3107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371258601, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371258788, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371258869, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371259844, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371259933, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371260152, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371262467, "dur": 1031, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371263521, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371263609, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371263961, "dur": 620, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371264591, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371265595, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403371265807, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371266426, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371267529, "dur": 103, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371268670, "dur": 248044, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371518861, "dur": 21281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751403371518353, "dur": 21960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371540972, "dur": 83, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371541941, "dur": 250947, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371794983, "dur": 19070, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751403371794969, "dur": 20781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371817011, "dur": 276, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403371817840, "dur": 96545, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403371921263, "dur": 397, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751403371921232, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751403371921702, "dur": 2516, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751403371924226, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371176447, "dur": 43839, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371220297, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371220518, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371220665, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_E43C745D5056CF76.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371220907, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371221330, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_676A4B919D1115D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371221652, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371221833, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371222337, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371222438, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BD82F057A3253721.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371222607, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371223262, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_CE65D8670DA73F5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371223321, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751403371223453, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751403371223585, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751403371223767, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751403371224401, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371224471, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751403371224651, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751403371224875, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371225729, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371226081, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751403371226388, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371226596, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371226794, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371227003, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371227392, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371227653, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371227869, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371228101, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371228340, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371228653, "dur": 1293, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Generation\\Processors\\GraphCompilationResult.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751403371228621, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371230186, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371230424, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371230634, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371231019, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\VirtualTextureInputMaterialSlot.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751403371230988, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371231877, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371232112, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371232375, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371232626, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371232842, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371233064, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371233296, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371233548, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371233920, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371234142, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371234363, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371234597, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371234826, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371235066, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371235308, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371236673, "dur": 1892, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Textures\\RTHandle.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751403371235702, "dur": 2863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371238603, "dur": 3544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Window\\TimelineWindow_EditorCallbacks.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751403371238584, "dur": 4351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371242965, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371243316, "dur": 1589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371244940, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371245007, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371245271, "dur": 5779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371251145, "dur": 54, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371251228, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371251445, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371252078, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371252324, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371252400, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371253009, "dur": 1612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371254624, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371254773, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371255016, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371255087, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371255793, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371255954, "dur": 4698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371260653, "dur": 4946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371265602, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403371265857, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371265927, "dur": 123984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371389913, "dur": 2463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371392438, "dur": 2438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371394933, "dur": 3750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371398689, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371399156, "dur": 2717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371401922, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371404282, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371406868, "dur": 3448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371410317, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371410445, "dur": 3861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371414308, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371414411, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751403371414511, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371414627, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371414712, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751403371415168, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751403371415726, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371415880, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371416022, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751403371416076, "dur": 102287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371518395, "dur": 68067, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751403371518366, "dur": 69703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371589376, "dur": 277, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371742205, "dur": 62, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371590665, "dur": 151639, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403371751417, "dur": 67224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751403371751401, "dur": 67242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751403371820670, "dur": 126, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403371818664, "dur": 2152, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751403371820822, "dur": 103470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371176496, "dur": 43812, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371220316, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371220982, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371221342, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_0197621F1ECC6D18.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371221769, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_F0DA661B5DCD99FC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371222297, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_FB81041CC44652D8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371222532, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751403371223161, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751403371223788, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403371223892, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403371223967, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403371224856, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371225127, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403371225214, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403371225719, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403371225892, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371225958, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403371226374, "dur": 602, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403371226977, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371227223, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371227432, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371227741, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371227965, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371228186, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371228430, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371228683, "dur": 1240, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Generation\\Descriptors\\BlockFieldDescriptor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751403371228658, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371230206, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371230656, "dur": 1188, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Nodes\\Artistic\\Adjustment\\SaturationNode.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751403371230425, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371231874, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371232103, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371232358, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371232586, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371232795, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371233006, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371233229, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371233456, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371233691, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371233908, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371234405, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Animation\\OnAnimatorIK.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751403371234212, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371234943, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371235192, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371235436, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371235702, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371235910, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371236191, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371236439, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371236734, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371237026, "dur": 1608, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnPointerEnterMessageListener.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751403371236977, "dur": 1989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371239947, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371240151, "dur": 4279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371244720, "dur": 2062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371246808, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371247002, "dur": 1787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371248867, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371249535, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371250009, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371250223, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371251105, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371251449, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371251642, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371252592, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371252909, "dur": 1770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371254682, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371255067, "dur": 1053, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371256121, "dur": 2242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371258366, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371258587, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371258789, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371259039, "dur": 1212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371260321, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371260380, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371260704, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371260811, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371261419, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371261518, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371261739, "dur": 1861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371263601, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371263665, "dur": 1191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371264911, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371265600, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403371265847, "dur": 124506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371390355, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371392560, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371395030, "dur": 2442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371397474, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371397630, "dur": 3317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371400949, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371401044, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371403567, "dur": 2484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371406099, "dur": 2855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371408956, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371409452, "dur": 2934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371412442, "dur": 2853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403371415298, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371415393, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751403371415824, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751403371415899, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371416072, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403371416713, "dur": 507550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371176547, "dur": 43777, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371220330, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_1AA5CC13572AAA47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371221232, "dur": 395, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FC3AA1C0027C0ADA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371221855, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_611E434C56DD2756.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371222957, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371223146, "dur": 3771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371227000, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371227235, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371227572, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371227770, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371227984, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371228216, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371228460, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371228843, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371229150, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371231024, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Nodes\\Math\\Range\\OneMinusNode.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403371230183, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371231779, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371232030, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371232288, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371232540, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371232957, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371233174, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371233411, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371233644, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371233883, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371234592, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371234919, "dur": 619, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_2_3.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403371234848, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371235692, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371236010, "dur": 875, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\RenderGraph\\Debug\\DebugDisplaySettingsRenderGraph.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403371235906, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371237527, "dur": 628, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnBecameInvisibleMessageListener.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403371237317, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371238354, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371238669, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371238808, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371238984, "dur": 12389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371251412, "dur": 469, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371251941, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371252974, "dur": 1186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371254273, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371255135, "dur": 884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371256137, "dur": 881, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_94F5458D7254E99F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371257044, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371258549, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371259858, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403371260045, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371260581, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371260657, "dur": 4951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371265609, "dur": 51369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371316979, "dur": 72947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371389928, "dur": 2150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371392140, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371394498, "dur": 2498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371397058, "dur": 3272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371400391, "dur": 2678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371403118, "dur": 6984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371410147, "dur": 2877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403371413085, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371413341, "dur": 768, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371414161, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371414420, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751403371414719, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371414952, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751403371415241, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751403371415614, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403371415864, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751403371416037, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751403371416117, "dur": 508170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371176589, "dur": 43750, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371220357, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CCD605E26FC2B53B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403371221055, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371221166, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_FE696A2A6E4C16EE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403371221242, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_FE696A2A6E4C16EE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403371221609, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_83F4E4F0FA017AFD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403371221682, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403371221851, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371223106, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403371223280, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403371223784, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403371224030, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403371224354, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751403371224552, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371224730, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751403371224855, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371225267, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751403371225463, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751403371226230, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371226403, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371226707, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371227249, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371227427, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371227869, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371228086, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371228324, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371228558, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371229072, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371229428, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371229721, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371230316, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371231021, "dur": 664, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Interfaces\\IGeneratesFunction.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751403371230963, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371231909, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371232143, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371232406, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371232640, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371232866, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371233078, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371233315, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371233552, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371233785, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371234025, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371234246, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371234503, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371234732, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371234954, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371235190, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371235434, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371235709, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371236066, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371236295, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371236503, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371237000, "dur": 692, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\SubtractionHandler.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751403371236728, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371237693, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371238208, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371238429, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371238651, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403371238839, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371241261, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371241443, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403371241749, "dur": 1649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371243399, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371243508, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403371243711, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371244315, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371244434, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371244568, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751403371246890, "dur": 306, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371248042, "dur": 50, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371248143, "dur": 138329, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 6, "ts": 1751403371389910, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371392446, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371394782, "dur": 2403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371397195, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371397279, "dur": 2841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371400122, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371400216, "dur": 2253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371402470, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371402587, "dur": 2120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371404759, "dur": 2220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371406981, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371407057, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371407190, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371409534, "dur": 2265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371411847, "dur": 2351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403371414200, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371414700, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371414835, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371414889, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751403371415161, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Toonshader.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751403371415394, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751403371415920, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371416014, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371416091, "dur": 335320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403371751438, "dur": 350, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751403371751412, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751403371751870, "dur": 2766, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751403371754643, "dur": 169640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371176628, "dur": 43724, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371220353, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BF2CDC1257CA045F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371221015, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371221245, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_111B28F0FC0CD3DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371221863, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371222347, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_C7DF13E3C14DE501.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371222568, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751403371222733, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371222927, "dur": 4472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371227431, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371227532, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371227690, "dur": 7771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371235571, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371235958, "dur": 2490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371238491, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371238641, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371238829, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371238985, "dur": 1467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371240455, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371240664, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371240926, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371241039, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371242049, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371242172, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371242436, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371243196, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371243335, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371243604, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371243826, "dur": 1390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371245218, "dur": 1078, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371246344, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371246513, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371246738, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371246884, "dur": 1545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371248461, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371249258, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371249868, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371250619, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371250811, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371250901, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371250994, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371251390, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371251670, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371251967, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371252254, "dur": 2111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371254407, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371254557, "dur": 1080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371255670, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371256209, "dur": 928, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371257181, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371257330, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371257603, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371258106, "dur": 1014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371259185, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371259270, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371259510, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371260342, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371260459, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371260792, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371261360, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371261458, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403371261686, "dur": 1237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371262925, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371263037, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371263298, "dur": 2358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371265656, "dur": 124562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371390220, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371392441, "dur": 2633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371395075, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371395153, "dur": 3794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371398952, "dur": 494, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371399466, "dur": 3628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371403135, "dur": 3327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371406463, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371406564, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371406638, "dur": 2907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371409546, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371409618, "dur": 4281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403371414114, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371414412, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751403371414760, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751403371414957, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371415181, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Toonshader.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751403371415429, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371415526, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751403371415683, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371415854, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751403371415969, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371416106, "dur": 505161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403371921288, "dur": 376, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751403371921268, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751403371921691, "dur": 2532, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751403371176674, "dur": 44260, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371220987, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_1AA34DA22D3674A5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371221340, "dur": 534, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_86082FB9E11717DE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371221880, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371222782, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371222897, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371223078, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751403371223554, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371223952, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371224063, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751403371224419, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371224761, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371224839, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371225258, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751403371225788, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371226072, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371226174, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371226262, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403371226326, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371226407, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371226614, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371226823, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371227237, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371227451, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371227855, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371228078, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371228334, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371228873, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\BuiltInFields.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403371228595, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371229820, "dur": 1166, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Util\\PooledHashSet.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403371231016, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Util\\KeywordUtil.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403371229711, "dur": 2094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371231806, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371232126, "dur": 1818, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing\\PostProcessing\\Editor\\Attributes\\PostProcessEditorAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403371232039, "dur": 2047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371234087, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371234322, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371235023, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371235267, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371236009, "dur": 1133, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_ColorGradient.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403371235498, "dur": 1974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371237523, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\IGraphElementWithDebugData.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403371238083, "dur": 858, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\IGraphElementDebugData.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403371237472, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371239118, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371239347, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371239924, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371240329, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371241392, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371242099, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371242324, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371243206, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_D58562F0DF82BCBA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371243445, "dur": 985, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371244450, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371244667, "dur": 4794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371249540, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371249757, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371250485, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371250816, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371251353, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371251590, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371251813, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371252176, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371252896, "dur": 2094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371254992, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403371255193, "dur": 1619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371256816, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371256970, "dur": 650, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371257663, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371258062, "dur": 2544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371260633, "dur": 4980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371265614, "dur": 125098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371390715, "dur": 2160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371392926, "dur": 2310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371395298, "dur": 4140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371399440, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371399689, "dur": 2311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371402002, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371402256, "dur": 2311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371404616, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371406950, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403371407071, "dur": 2464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371409577, "dur": 3022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371412650, "dur": 3949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403371416683, "dur": 507578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403371933670, "dur": 1674, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5212, "tid": 745, "ts": 1751403371969339, "dur": 5704, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5212, "tid": 745, "ts": 1751403371975125, "dur": 6122, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5212, "tid": 745, "ts": 1751403371958736, "dur": 24431, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}