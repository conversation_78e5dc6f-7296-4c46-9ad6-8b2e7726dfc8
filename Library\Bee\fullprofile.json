{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5212, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5212, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5212, "tid": 717, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5212, "tid": 717, "ts": 1751403173120185, "dur": 779, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5212, "tid": 717, "ts": 1751403173125377, "dur": 1522, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5212, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5212, "tid": 1, "ts": 1751403167594106, "dur": 8696, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751403167602809, "dur": 88487, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5212, "tid": 1, "ts": 1751403167691306, "dur": 64181, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5212, "tid": 717, "ts": 1751403173126908, "dur": 23, "ph": "X", "name": "", "args": {}}, {"pid": 5212, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167590489, "dur": 18688, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167609179, "dur": 5498765, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167610763, "dur": 3095, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167613869, "dur": 2400, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167616275, "dur": 558, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167616840, "dur": 26, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167616871, "dur": 83, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167616960, "dur": 4, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167616966, "dur": 80, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617055, "dur": 2, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617059, "dur": 87, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617150, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617152, "dur": 61, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617217, "dur": 2, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617219, "dur": 51, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617274, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617276, "dur": 42, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617321, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617322, "dur": 159, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617487, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617547, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617549, "dur": 39, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617591, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617593, "dur": 40, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617637, "dur": 1, "ph": "X", "name": "ProcessMessages 107", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617639, "dur": 48, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617691, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617694, "dur": 45, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617742, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617745, "dur": 48, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617796, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617799, "dur": 46, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617849, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617852, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617906, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617909, "dur": 45, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617956, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167617959, "dur": 53, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618015, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618018, "dur": 53, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618074, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618077, "dur": 47, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618127, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618130, "dur": 54, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618187, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618190, "dur": 45, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618238, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618241, "dur": 44, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618288, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618291, "dur": 54, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618347, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618350, "dur": 46, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618399, "dur": 2, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618402, "dur": 44, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618449, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618451, "dur": 49, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618503, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618506, "dur": 51, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618560, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618563, "dur": 36, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618602, "dur": 118, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618725, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618770, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618773, "dur": 42, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618817, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618819, "dur": 44, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618867, "dur": 44, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618912, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618914, "dur": 37, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167618954, "dur": 45, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619001, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619002, "dur": 44, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619048, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619050, "dur": 42, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619094, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619096, "dur": 42, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619140, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619142, "dur": 43, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619186, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619188, "dur": 44, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619234, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619235, "dur": 39, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619278, "dur": 34, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619315, "dur": 40, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619358, "dur": 43, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619403, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619405, "dur": 43, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619450, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619452, "dur": 44, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619497, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619499, "dur": 37, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619538, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619539, "dur": 42, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619584, "dur": 42, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619629, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619630, "dur": 43, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619676, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619677, "dur": 43, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619724, "dur": 34, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619762, "dur": 42, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619808, "dur": 43, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619853, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619854, "dur": 45, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619901, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619902, "dur": 43, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619949, "dur": 34, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167619987, "dur": 44, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620033, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620035, "dur": 45, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620082, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620084, "dur": 43, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620129, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620132, "dur": 35, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620168, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620170, "dur": 35, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620208, "dur": 28, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620239, "dur": 34, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620277, "dur": 38, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620318, "dur": 86, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620409, "dur": 48, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620460, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620462, "dur": 40, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620505, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620507, "dur": 49, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620559, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620562, "dur": 48, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620613, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620615, "dur": 46, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620665, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620667, "dur": 47, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620719, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620721, "dur": 56, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620781, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620784, "dur": 65, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620853, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620856, "dur": 49, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620908, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620911, "dur": 46, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620960, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620962, "dur": 30, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620996, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167620999, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621048, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621051, "dur": 46, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621101, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621104, "dur": 46, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621153, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621157, "dur": 43, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621203, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621205, "dur": 72, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621281, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621331, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621333, "dur": 46, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621382, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621384, "dur": 45, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621432, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621435, "dur": 41, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621478, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621480, "dur": 46, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621529, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621532, "dur": 91, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621626, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621628, "dur": 47, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621678, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621680, "dur": 70, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621757, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621820, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621823, "dur": 50, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621876, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621879, "dur": 52, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621934, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621937, "dur": 42, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621982, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167621984, "dur": 212, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622201, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622254, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622257, "dur": 51, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622311, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622314, "dur": 50, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622366, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622369, "dur": 47, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622419, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622422, "dur": 46, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622470, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622473, "dur": 93, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622570, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622572, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622634, "dur": 2, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622637, "dur": 47, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622687, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622689, "dur": 47, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622740, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622742, "dur": 40, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622785, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622787, "dur": 43, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622833, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622836, "dur": 46, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622885, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622887, "dur": 46, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622936, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622938, "dur": 44, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622987, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167622989, "dur": 37, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623029, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623031, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623075, "dur": 45, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623123, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623125, "dur": 45, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623173, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623176, "dur": 48, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623227, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623229, "dur": 37, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623269, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623271, "dur": 43, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623317, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623319, "dur": 45, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623367, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623370, "dur": 45, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623418, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623420, "dur": 40, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623463, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623465, "dur": 39, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623509, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623511, "dur": 57, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623572, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623575, "dur": 54, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623632, "dur": 2, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623635, "dur": 49, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623687, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623690, "dur": 42, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623735, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623737, "dur": 48, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623788, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623790, "dur": 53, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623846, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623848, "dur": 37, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623889, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623891, "dur": 43, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623937, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623940, "dur": 42, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623986, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167623988, "dur": 45, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624037, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624039, "dur": 35, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624076, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624078, "dur": 38, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624120, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624123, "dur": 38, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624164, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624166, "dur": 45, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624214, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624217, "dur": 41, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624261, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624263, "dur": 46, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624312, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624315, "dur": 42, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624360, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624362, "dur": 45, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624410, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624412, "dur": 52, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624467, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624469, "dur": 50, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624523, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624525, "dur": 51, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624579, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624581, "dur": 37, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624622, "dur": 1, "ph": "X", "name": "ProcessMessages 85", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624624, "dur": 44, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624671, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624672, "dur": 47, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624722, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624724, "dur": 44, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624868, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624871, "dur": 84, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624959, "dur": 2, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167624963, "dur": 52, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625020, "dur": 42, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625067, "dur": 33, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625103, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625105, "dur": 34, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625143, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625145, "dur": 47, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625195, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625198, "dur": 75, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625279, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625283, "dur": 55, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625342, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625346, "dur": 75, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625425, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625429, "dur": 53, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625486, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625489, "dur": 55, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625549, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625601, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625604, "dur": 38, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625644, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625646, "dur": 50, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625700, "dur": 2, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625703, "dur": 43, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625749, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625752, "dur": 42, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625800, "dur": 44, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625847, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625850, "dur": 43, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625898, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625954, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167625956, "dur": 50, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626009, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626011, "dur": 49, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626063, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626065, "dur": 39, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626109, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626112, "dur": 45, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626160, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626163, "dur": 76, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626245, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626335, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626337, "dur": 66, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626406, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626409, "dur": 51, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626465, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626467, "dur": 46, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626517, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626519, "dur": 54, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626576, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626578, "dur": 65, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626647, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626649, "dur": 42, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626697, "dur": 194, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626895, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626898, "dur": 48, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626950, "dur": 2, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167626954, "dur": 52, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627008, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627011, "dur": 51, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627065, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627068, "dur": 50, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627121, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627123, "dur": 47, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627173, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627176, "dur": 50, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627228, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627232, "dur": 50, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627286, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627288, "dur": 48, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627339, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627342, "dur": 44, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627389, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627391, "dur": 52, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627446, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627448, "dur": 45, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627498, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627501, "dur": 47, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627551, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627553, "dur": 42, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627597, "dur": 1, "ph": "X", "name": "ProcessMessages 85", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627599, "dur": 47, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627651, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627703, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627705, "dur": 44, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627752, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627755, "dur": 52, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627810, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627812, "dur": 48, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627863, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627865, "dur": 45, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627914, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627917, "dur": 48, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627968, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167627970, "dur": 50, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628023, "dur": 1, "ph": "X", "name": "ProcessMessages 771", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628026, "dur": 57, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628086, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628089, "dur": 42, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628135, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628138, "dur": 42, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628183, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628185, "dur": 82, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628270, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628272, "dur": 38, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628313, "dur": 1, "ph": "X", "name": "ProcessMessages 129", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628314, "dur": 44, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628362, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628364, "dur": 45, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628412, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628414, "dur": 55, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628473, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628522, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628524, "dur": 50, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628577, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628579, "dur": 78, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628660, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628663, "dur": 37, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628703, "dur": 1, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628705, "dur": 41, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628748, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628751, "dur": 41, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628795, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628797, "dur": 79, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628879, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628881, "dur": 35, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628919, "dur": 10, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628930, "dur": 44, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628978, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167628998, "dur": 42, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629043, "dur": 245, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629290, "dur": 122, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629417, "dur": 5, "ph": "X", "name": "ProcessMessages 3992", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629423, "dur": 38, "ph": "X", "name": "ReadAsync 3992", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629465, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629467, "dur": 42, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629514, "dur": 43, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629561, "dur": 8, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629570, "dur": 81, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629654, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629656, "dur": 37, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629698, "dur": 45, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629747, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629749, "dur": 37, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629789, "dur": 1, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629791, "dur": 60, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629855, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629858, "dur": 59, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629920, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629923, "dur": 46, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629972, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167629974, "dur": 48, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630025, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630027, "dur": 48, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630078, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630081, "dur": 54, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630138, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630141, "dur": 46, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630190, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630192, "dur": 42, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630237, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630239, "dur": 45, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630287, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630289, "dur": 36, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630329, "dur": 37, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630370, "dur": 47, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630420, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630423, "dur": 40, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630466, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630468, "dur": 45, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630516, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630519, "dur": 46, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630569, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630571, "dur": 45, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630619, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630622, "dur": 47, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630672, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630674, "dur": 51, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630729, "dur": 2, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630732, "dur": 49, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630785, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630788, "dur": 47, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630838, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630840, "dur": 46, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630890, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630892, "dur": 49, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630944, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167630946, "dur": 51, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631000, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631003, "dur": 47, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631053, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631056, "dur": 166, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631225, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631228, "dur": 69, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631300, "dur": 3, "ph": "X", "name": "ProcessMessages 2242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631304, "dur": 47, "ph": "X", "name": "ReadAsync 2242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631354, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631357, "dur": 44, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631403, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631406, "dur": 46, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631454, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631456, "dur": 46, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631506, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631508, "dur": 48, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631560, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631562, "dur": 42, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631607, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631610, "dur": 45, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631657, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631659, "dur": 41, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631703, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631705, "dur": 60, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631768, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631771, "dur": 54, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631829, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631832, "dur": 54, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631889, "dur": 2, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631892, "dur": 57, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631953, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167631956, "dur": 54, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632013, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632016, "dur": 46, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632066, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632069, "dur": 49, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632121, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632124, "dur": 52, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632180, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632183, "dur": 53, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632238, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632241, "dur": 50, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632294, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632296, "dur": 50, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632350, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632353, "dur": 48, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632404, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632406, "dur": 48, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632458, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632461, "dur": 53, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632518, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632520, "dur": 47, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632570, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632573, "dur": 42, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632618, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632620, "dur": 49, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632673, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632675, "dur": 43, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632722, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632724, "dur": 44, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632773, "dur": 48, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632824, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632826, "dur": 48, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632878, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632880, "dur": 46, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632929, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632932, "dur": 49, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632983, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167632986, "dur": 46, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633035, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633037, "dur": 44, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633084, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633087, "dur": 38, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633127, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633130, "dur": 112, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633248, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633287, "dur": 35, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633325, "dur": 1, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633328, "dur": 46, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633378, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633381, "dur": 47, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633431, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633434, "dur": 40, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633477, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633480, "dur": 36, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633519, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633521, "dur": 39, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633564, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633566, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633605, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633607, "dur": 43, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633653, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633655, "dur": 52, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633711, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633713, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633752, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633755, "dur": 80, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633840, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633888, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167633890, "dur": 114, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634012, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634062, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634065, "dur": 41, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634110, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634112, "dur": 38, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634156, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634158, "dur": 40, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634203, "dur": 42, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634248, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634250, "dur": 158, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634413, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634468, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634471, "dur": 49, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634523, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634525, "dur": 39, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634569, "dur": 121, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634695, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634743, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634745, "dur": 44, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634793, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634796, "dur": 77, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634876, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634878, "dur": 44, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634926, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167634929, "dur": 106, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635041, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635099, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635102, "dur": 49, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635154, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635157, "dur": 148, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635308, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635310, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635376, "dur": 2, "ph": "X", "name": "ProcessMessages 937", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635379, "dur": 40, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635422, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635424, "dur": 78, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635505, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635507, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635561, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635564, "dur": 50, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635617, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635620, "dur": 41, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635665, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635667, "dur": 113, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635787, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635845, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635848, "dur": 49, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635900, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635902, "dur": 38, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635943, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167635945, "dur": 118, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636069, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636123, "dur": 1, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636126, "dur": 41, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636179, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636181, "dur": 63, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636249, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636298, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636301, "dur": 46, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636349, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636351, "dur": 138, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636493, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636538, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636541, "dur": 95, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636639, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636642, "dur": 40, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636685, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636687, "dur": 44, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636734, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636736, "dur": 38, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636778, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636780, "dur": 44, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636826, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636827, "dur": 76, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636908, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636949, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636950, "dur": 38, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636990, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167636992, "dur": 30, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637026, "dur": 75, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637105, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637144, "dur": 39, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637185, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637187, "dur": 32, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637222, "dur": 84, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637309, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637350, "dur": 33, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637386, "dur": 33, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637421, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637422, "dur": 83, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637508, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637542, "dur": 44, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637588, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637591, "dur": 48, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637643, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637645, "dur": 72, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637722, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637774, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637778, "dur": 42, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637823, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637825, "dur": 73, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637904, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637952, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167637954, "dur": 44, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638002, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638006, "dur": 79, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638090, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638140, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638142, "dur": 45, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638190, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638192, "dur": 77, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638273, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638322, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638324, "dur": 46, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638373, "dur": 2, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638376, "dur": 70, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638451, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638504, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638507, "dur": 54, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638565, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638568, "dur": 45, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638616, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638618, "dur": 78, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638702, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638751, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638754, "dur": 46, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638803, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638806, "dur": 46, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638855, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638858, "dur": 45, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638906, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638908, "dur": 42, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638953, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638955, "dur": 39, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638997, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167638999, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639064, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639112, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639114, "dur": 44, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639161, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639163, "dur": 76, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639244, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639294, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639296, "dur": 44, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639343, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639346, "dur": 78, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639426, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639428, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639484, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639488, "dur": 45, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639536, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639539, "dur": 66, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639610, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639665, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639668, "dur": 45, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639717, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639720, "dur": 56, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639779, "dur": 1, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639783, "dur": 55, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639842, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639845, "dur": 55, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639903, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639906, "dur": 40, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639950, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639953, "dur": 41, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639996, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167639999, "dur": 97, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640101, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640158, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640160, "dur": 45, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640218, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640220, "dur": 45, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640268, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640271, "dur": 102, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640378, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640380, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640445, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640448, "dur": 49, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640501, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640504, "dur": 44, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640551, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640553, "dur": 91, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640651, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640707, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640710, "dur": 49, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640761, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640764, "dur": 50, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640818, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640820, "dur": 51, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640874, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640877, "dur": 48, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640939, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640941, "dur": 38, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640982, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167640984, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641058, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641118, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641121, "dur": 49, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641174, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641176, "dur": 43, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641224, "dur": 24, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641250, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641254, "dur": 82, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641340, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641403, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641406, "dur": 133, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641543, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641546, "dur": 76, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641628, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641681, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641684, "dur": 45, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641733, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641736, "dur": 39, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641778, "dur": 1, "ph": "X", "name": "ProcessMessages 129", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641781, "dur": 43, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641827, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641830, "dur": 137, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167641973, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642020, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642023, "dur": 39, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642066, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642068, "dur": 45, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642117, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642120, "dur": 46, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642169, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642171, "dur": 34, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642210, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642213, "dur": 132, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642351, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642407, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642409, "dur": 46, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642458, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642460, "dur": 33, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642497, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642499, "dur": 63, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642567, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642624, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642626, "dur": 96, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642728, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642782, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642785, "dur": 51, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642839, "dur": 3, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642843, "dur": 39, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642885, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642887, "dur": 97, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167642990, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643043, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643047, "dur": 49, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643099, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643101, "dur": 40, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643145, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643148, "dur": 115, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643269, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643328, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643331, "dur": 52, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643386, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643389, "dur": 38, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643431, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643433, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643513, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643566, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643569, "dur": 42, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643616, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643675, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643678, "dur": 82, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643764, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643766, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643823, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643826, "dur": 50, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643880, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643883, "dur": 39, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643925, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167643927, "dur": 79, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644010, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644012, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644075, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644078, "dur": 46, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644127, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644130, "dur": 82, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644217, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644275, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644279, "dur": 52, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644334, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644337, "dur": 88, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644431, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644482, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644485, "dur": 49, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644536, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644539, "dur": 46, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644588, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644591, "dur": 84, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644679, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644735, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644738, "dur": 48, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644789, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644791, "dur": 92, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644888, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644955, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167644959, "dur": 46, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645009, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645012, "dur": 89, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645106, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645162, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645165, "dur": 48, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645216, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645218, "dur": 38, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645259, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645262, "dur": 112, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645378, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645433, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645436, "dur": 47, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645486, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645489, "dur": 38, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645530, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645533, "dur": 75, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645610, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645612, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645667, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645670, "dur": 47, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645720, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645723, "dur": 39, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645765, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645768, "dur": 72, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645843, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645846, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645902, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645907, "dur": 46, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645955, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167645958, "dur": 89, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646052, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646106, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646109, "dur": 56, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646168, "dur": 2, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646171, "dur": 35, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646209, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646211, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646325, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646380, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646383, "dur": 55, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646441, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646444, "dur": 39, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646487, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646555, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646609, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646612, "dur": 52, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646667, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646670, "dur": 38, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646710, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646712, "dur": 68, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646785, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646839, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646841, "dur": 51, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646895, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646898, "dur": 42, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646943, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167646945, "dur": 70, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647020, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647077, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647079, "dur": 50, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647132, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647135, "dur": 44, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647182, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647188, "dur": 78, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647271, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647329, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647333, "dur": 66, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647402, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647405, "dur": 72, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647481, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647483, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647539, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647542, "dur": 58, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647603, "dur": 2, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647607, "dur": 40, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647649, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647652, "dur": 83, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647738, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647741, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647801, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647804, "dur": 52, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647859, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647861, "dur": 40, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647904, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167647907, "dur": 90, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648003, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648061, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648066, "dur": 44, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648112, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648115, "dur": 43, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648162, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648208, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648210, "dur": 90, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648304, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648307, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648362, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648365, "dur": 59, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648427, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648430, "dur": 38, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648472, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648475, "dur": 48, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648526, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648529, "dur": 38, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648570, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648572, "dur": 42, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648618, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648620, "dur": 42, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648668, "dur": 38, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648712, "dur": 51, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648766, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648768, "dur": 50, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648821, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648823, "dur": 42, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648870, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648872, "dur": 39, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648914, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648917, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648964, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167648966, "dur": 123, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649094, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649146, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649151, "dur": 49, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649203, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649205, "dur": 41, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649249, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649252, "dur": 45, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649302, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649355, "dur": 2, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649358, "dur": 47, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649408, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649411, "dur": 41, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649455, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649457, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649543, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649594, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649596, "dur": 48, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649647, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649650, "dur": 48, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649702, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649705, "dur": 47, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649756, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649759, "dur": 42, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649803, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649807, "dur": 105, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649918, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649972, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167649975, "dur": 199, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650178, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650180, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650224, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650227, "dur": 128, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650358, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650360, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650408, "dur": 420, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650831, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650897, "dur": 5, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650903, "dur": 38, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650947, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650984, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167650986, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651027, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651030, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651067, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651094, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651135, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651138, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651175, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651178, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651217, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651220, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651267, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651269, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651307, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651310, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651349, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651351, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651398, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651402, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651445, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651447, "dur": 39, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651491, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651493, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651551, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651553, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651579, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651581, "dur": 172, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651756, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651759, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651814, "dur": 3, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651818, "dur": 40, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651861, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651864, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651903, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651906, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651946, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651951, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651993, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167651996, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652035, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652037, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652075, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652077, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652114, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652118, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652154, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652157, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652195, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652198, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652233, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652236, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652274, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652276, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652315, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652317, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652353, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652355, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652392, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652396, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652434, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652437, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652477, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652480, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652516, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652518, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652556, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652558, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652598, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652601, "dur": 18, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652620, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652622, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652652, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652655, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652689, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652692, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652729, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652731, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652768, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652770, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652806, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652808, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652848, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652852, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652887, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652890, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652927, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652929, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652965, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167652967, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653004, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653006, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653040, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653043, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653077, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653080, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653120, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653125, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653162, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653164, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653201, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653204, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653241, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653243, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653275, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653277, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653315, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653318, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653356, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653358, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653400, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653402, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653441, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653443, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653482, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653485, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653520, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653523, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653559, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653563, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653602, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653605, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653639, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653641, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653678, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653681, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653716, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653718, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653765, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653768, "dur": 34, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653806, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653809, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653847, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653849, "dur": 33, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653888, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653890, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653925, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653928, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653962, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167653965, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654002, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654005, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654040, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654042, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654080, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654083, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654122, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654124, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654166, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654168, "dur": 36, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654208, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654211, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654250, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654252, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654291, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654294, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654330, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654332, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654376, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654379, "dur": 584, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654970, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167654974, "dur": 85, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655064, "dur": 8, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655074, "dur": 80, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655159, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655162, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655207, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655210, "dur": 60, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655276, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655322, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655324, "dur": 43, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655371, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655374, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655412, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655414, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655454, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655457, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655496, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655499, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655539, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655542, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655581, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655583, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655619, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655621, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655661, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655664, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655701, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655704, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655740, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655743, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655778, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655780, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655823, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655826, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655862, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655864, "dur": 30, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655897, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655899, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655932, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655935, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655974, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167655977, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656016, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656019, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656063, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656067, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656102, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656104, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656140, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656142, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656183, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656185, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656220, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656223, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656257, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656261, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656299, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656302, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656341, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656343, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656386, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656389, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656431, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656467, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656470, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656512, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656550, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656552, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656660, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656698, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656700, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656741, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167656744, "dur": 380, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657130, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657176, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657179, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657234, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657236, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657274, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657277, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657355, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657357, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657399, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167657402, "dur": 7312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167664722, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167664726, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167664786, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167664790, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167664842, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167664844, "dur": 958, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167665806, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167665811, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167665857, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167665859, "dur": 1571, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667437, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667447, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667499, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667502, "dur": 229, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667735, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667738, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667802, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667805, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667877, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667916, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167667919, "dur": 263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668186, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668188, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668236, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668239, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668278, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668280, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668344, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668346, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668386, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668478, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668481, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668524, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668526, "dur": 282, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668813, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668847, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668850, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668952, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668955, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668993, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167668998, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669032, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669034, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669211, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669247, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669283, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669285, "dur": 186, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669477, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669479, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669516, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669518, "dur": 389, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669911, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669913, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669949, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167669951, "dur": 121, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670075, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670078, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670130, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670132, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670175, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670178, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670211, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670212, "dur": 170, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670388, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670425, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670427, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670461, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670493, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670495, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670595, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670629, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670661, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670663, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670776, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670809, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670820, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670856, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670858, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670892, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670963, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167670997, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671000, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671032, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671035, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671100, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671130, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671132, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671250, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671285, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671287, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671325, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671356, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671358, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671409, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671412, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671449, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671452, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671486, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671674, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671677, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671712, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671716, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671752, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671756, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671825, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671828, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671866, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671868, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671907, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671909, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671945, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167671947, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672127, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672159, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672161, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672196, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672200, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672321, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672323, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672357, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672359, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672495, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672532, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672534, "dur": 165, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672705, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672707, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672743, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672745, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672793, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672828, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672830, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672874, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672907, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672909, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167672945, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673038, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673040, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673073, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673075, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673188, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673221, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673223, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673312, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673314, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673348, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673350, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673649, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673683, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673685, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673800, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673803, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673843, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673881, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167673883, "dur": 377, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674264, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674267, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674302, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674304, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674405, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674440, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674442, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674475, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674477, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674529, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674564, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674567, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674601, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674873, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674920, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167674922, "dur": 263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675189, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675191, "dur": 168, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675363, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675366, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675426, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675463, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675465, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675622, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675666, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675668, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675768, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675801, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167675803, "dur": 311, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676118, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676120, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676157, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676160, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676198, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676200, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676243, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676245, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676282, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676284, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676322, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676324, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676357, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676389, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676482, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676485, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676524, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676526, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676589, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676640, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676643, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676697, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676700, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676815, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676817, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676853, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167676857, "dur": 146, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677015, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677017, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677057, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677060, "dur": 411, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677475, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677477, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677514, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677548, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677559, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677597, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677599, "dur": 36, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677639, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677641, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677676, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677678, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677844, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677846, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677879, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167677881, "dur": 388, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678273, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678275, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678311, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678313, "dur": 158, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678475, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678477, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678515, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678517, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678550, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678552, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678622, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678656, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678915, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678964, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167678966, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679002, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679035, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679037, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679326, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679362, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679364, "dur": 379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679747, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679749, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679786, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679788, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679822, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679824, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167679959, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167680002, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167680004, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167680114, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167680155, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167680157, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167680327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167680329, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167680367, "dur": 826, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681197, "dur": 50, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681250, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681254, "dur": 150, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681410, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681464, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681466, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681527, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681563, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681565, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681798, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681801, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681838, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681840, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681907, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681946, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681948, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681989, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167681991, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167682184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167682186, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167682225, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167682227, "dur": 1953, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684189, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684193, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684246, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684250, "dur": 196, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684451, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684454, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684497, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684500, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684538, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684540, "dur": 211, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684754, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684756, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684808, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684810, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684851, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167684854, "dur": 474, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685331, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685334, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685378, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685380, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685449, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685487, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685490, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685536, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685539, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685579, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685581, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685623, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685626, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685663, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685665, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685772, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685811, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685813, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685849, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167685851, "dur": 153, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686008, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686039, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686041, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686132, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686134, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686170, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686173, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686415, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686447, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686448, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686498, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686500, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686538, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686540, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686789, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686791, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686826, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167686832, "dur": 192, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687028, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687030, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687067, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687069, "dur": 301, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687374, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687376, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687417, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687419, "dur": 166, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687592, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687633, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687636, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687706, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687744, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687747, "dur": 186, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687937, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687940, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687980, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167687983, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688112, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688114, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688161, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688163, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688201, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688203, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688348, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688350, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688390, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688392, "dur": 400, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688799, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688851, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688854, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688917, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688919, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688961, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167688963, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689023, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689025, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689060, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689062, "dur": 490, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689557, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689559, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689601, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689604, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689675, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689679, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689723, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689726, "dur": 152, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689882, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689884, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689921, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167689923, "dur": 523, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167690450, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167690453, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167690496, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167690498, "dur": 228, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167690729, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167690731, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167690773, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167690775, "dur": 1380, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167692163, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167692167, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167692225, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167692231, "dur": 94669, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167786909, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167786913, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167786978, "dur": 2685, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167789670, "dur": 3760, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167793438, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167793442, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167793495, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167793498, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167793567, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167793596, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167793598, "dur": 1006, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167794609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167794611, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167794651, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167794654, "dur": 760, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795418, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795420, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795450, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795453, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795718, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795721, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795765, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795767, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795873, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795876, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795915, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795918, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167795982, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167796018, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167796021, "dur": 284, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167796309, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167796312, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167796338, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167796340, "dur": 1111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167797455, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167797458, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167797494, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167797499, "dur": 751, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798254, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798256, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798293, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798296, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798343, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798347, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798454, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798456, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798495, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798497, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798762, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798765, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798804, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167798806, "dur": 1086, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167799915, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167799918, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167799976, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167799979, "dur": 561, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800548, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800589, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800592, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800746, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800748, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800801, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800803, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800871, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800913, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167800916, "dur": 796, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167801719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167801721, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167801774, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167801777, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167801934, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167801976, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167801979, "dur": 629, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167802611, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167802614, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167802651, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167802653, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167802801, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167802837, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167802840, "dur": 337, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167803180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167803183, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167803225, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167803227, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167803261, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167803263, "dur": 1024, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167804291, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167804294, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167804332, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167804335, "dur": 554, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167804893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167804895, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167804931, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167804933, "dur": 223, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805161, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805163, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805202, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805204, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805539, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805542, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805576, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805578, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805727, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805729, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805766, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167805769, "dur": 243, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167806015, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167806017, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167806052, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167806055, "dur": 773, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167806836, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167806866, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167806868, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167806998, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807031, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807033, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807243, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807246, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807286, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807289, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807441, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807443, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807483, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167807486, "dur": 645, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808136, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808138, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808179, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808182, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808361, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808363, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808408, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808420, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808711, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808713, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808751, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167808753, "dur": 952, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167809713, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167809717, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167809769, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167809772, "dur": 705, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167810480, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167810483, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167810516, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167810518, "dur": 380, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167810903, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167810937, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167810940, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167811095, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167811131, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167811216, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167811256, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167811258, "dur": 658, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167811921, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167811974, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167811976, "dur": 188, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812169, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812203, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812206, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812286, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812288, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812321, "dur": 9, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812331, "dur": 578, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812914, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812950, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812952, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167812975, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813005, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813009, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813042, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813044, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813081, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813085, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813142, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813145, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813187, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813190, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813227, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813297, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813332, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813334, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813395, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813397, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813432, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813434, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813470, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813472, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813508, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813510, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813546, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813549, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813581, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813583, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813629, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813631, "dur": 35, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813669, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813672, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813708, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813712, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813748, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813750, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813774, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813776, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813808, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813811, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813839, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813841, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813878, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813881, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813925, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813928, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813990, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167813993, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814041, "dur": 3, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814070, "dur": 69, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814143, "dur": 5, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814150, "dur": 44, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814198, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814202, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814250, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814254, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814299, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814302, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814351, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814354, "dur": 41, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814399, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814403, "dur": 42, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814450, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814454, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814509, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814512, "dur": 154, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814673, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814713, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167814715, "dur": 541, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815260, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815263, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815312, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815315, "dur": 151, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815469, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815471, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815519, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815522, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815565, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403167815567, "dur": 1498908, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169314486, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169314491, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169314533, "dur": 29, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169314563, "dur": 18489, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169333084, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169333094, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169333156, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169333161, "dur": 52659, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169385830, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169385834, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169385889, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169385893, "dur": 119922, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169505825, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169505829, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169505881, "dur": 23, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169505906, "dur": 7621, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169513535, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169513538, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169513578, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169513581, "dur": 1659, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169515246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169515249, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169515299, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169515328, "dur": 33563, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169548903, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169548908, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169548975, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169548981, "dur": 1609, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169550600, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169550605, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169550725, "dur": 35, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403169550763, "dur": 3414855, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403172965626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403172965629, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403172965676, "dur": 21, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403172965698, "dur": 24085, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403172989791, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403172989793, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403172989855, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403172989859, "dur": 97479, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173087347, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173087349, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173087395, "dur": 18, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173087416, "dur": 7157, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173094582, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173094600, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173094652, "dur": 4, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173094658, "dur": 1884, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173096550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173096553, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173096615, "dur": 35, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173096688, "dur": 129, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173096822, "dur": 21, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173096845, "dur": 488, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173097337, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173097340, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173097388, "dur": 393, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5212, "tid": 12884901888, "ts": 1751403173097784, "dur": 9960, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5212, "tid": 717, "ts": 1751403173126933, "dur": 3399, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5212, "tid": 8589934592, "ts": 1751403167586216, "dur": 169306, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751403167755525, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5212, "tid": 8589934592, "ts": 1751403167755531, "dur": 1137, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5212, "tid": 717, "ts": 1751403173130335, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5212, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403167560098, "dur": 5548903, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403167564418, "dur": 10715, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403173109285, "dur": 6546, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403173113292, "dur": 144, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5212, "tid": 4294967296, "ts": 1751403173115955, "dur": 18, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5212, "tid": 717, "ts": 1751403173130344, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751403167605418, "dur": 2706, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403167608138, "dur": 1716, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403167610094, "dur": 93, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751403167610188, "dur": 445, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403167612234, "dur": 894, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403167614727, "dur": 2321, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403167617058, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751403167617125, "dur": 183, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751403167618773, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403167620483, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751403167625140, "dur": 141, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_46EB9B6930B27991.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403167625302, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751403167625418, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403167625481, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751403167625556, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403167625929, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403167626809, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751403167626912, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403167628407, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403167628797, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403167629012, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751403167629514, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403167629793, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751403167630458, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751403167632904, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751403167633406, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751403167633665, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403167633756, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403167634235, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403167634326, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403167635008, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403167635918, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751403167636198, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751403167641738, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751403167641830, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751403167641895, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403167642200, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751403167642297, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751403167647966, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403167648570, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751403167648699, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751403167648803, "dur": 156, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751403167648964, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751403167610668, "dur": 39709, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403167650402, "dur": 5446463, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403173096866, "dur": 305, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403173097344, "dur": 91, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751403173097482, "dur": 1899, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751403167611128, "dur": 39336, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167650468, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167650909, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167651134, "dur": 333, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167651728, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167651937, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_EA250D0B58FB5B36.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167652369, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_D380F5A76DBED59A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167652768, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167653111, "dur": 3737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167656987, "dur": 982, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751403167656938, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167658316, "dur": 1042, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\SolutionParser.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751403167658172, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167659414, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167659662, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167659935, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167660201, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167660443, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167660661, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167660879, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167661093, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167661332, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167661532, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167661730, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167662433, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167662661, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167662876, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167663085, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167663303, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167663508, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167663719, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167664065, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167664280, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167664493, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167664714, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167664999, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167665226, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167665465, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167665678, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167665913, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167666131, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167666387, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167666604, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167666865, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167667081, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167667316, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167667520, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167667829, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167668070, "dur": 3776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167671947, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167672018, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167672323, "dur": 2230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167674610, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_656D7410E7809ECD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167674730, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751403167675964, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167677711, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751403167679771, "dur": 143, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167680539, "dur": 106569, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751403167791101, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167793624, "dur": 2383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167796067, "dur": 2337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167798447, "dur": 2229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167800737, "dur": 2217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167803003, "dur": 2312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167805356, "dur": 2216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167807574, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403167807639, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167809952, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167812367, "dur": 2447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751403167814885, "dur": 1698457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751403169513373, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751403169513344, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751403169513713, "dur": 1715, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751403169515433, "dur": 3581436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167611047, "dur": 39371, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167650454, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167651126, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_111B28F0FC0CD3DB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167651213, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_4DE8BB45CF6CF9CC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167651332, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167651683, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167651922, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_611E434C56DD2756.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167652583, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167652785, "dur": 4491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167657358, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167657584, "dur": 7247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167664928, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167665203, "dur": 776, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167665993, "dur": 1560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167667707, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167667946, "dur": 2658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167670682, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167670857, "dur": 730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167671588, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167672120, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167672326, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167672519, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167672711, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167672920, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167673233, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167673877, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167674461, "dur": 1796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167676572, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167676770, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751403167677836, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167678100, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167678336, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167678535, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167678817, "dur": 7098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167686022, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167686205, "dur": 1315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167687593, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751403167687784, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167688374, "dur": 103163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167791539, "dur": 1997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167793538, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167793618, "dur": 2214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167795833, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167795912, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167798314, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167798524, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167800930, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167801008, "dur": 2325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167803379, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167805737, "dur": 2536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167808321, "dur": 2305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167810677, "dur": 2376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403167813395, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167813517, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167814579, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403167814675, "dur": 1501876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403169316580, "dur": 64505, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751403169316553, "dur": 66793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403169385442, "dur": 524, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751403169386759, "dur": 119241, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751403169513337, "dur": 35678, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751403169513307, "dur": 35710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751403169549047, "dur": 1721, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751403169550774, "dur": 3546131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167611110, "dur": 39324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167650457, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167650544, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751403167651285, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_0197621F1ECC6D18.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167651401, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167651459, "dur": 426, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_94DD634DECCA9836.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167651891, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_681D101710621A59.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167652418, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751403167652634, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167652879, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751403167653425, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751403167653694, "dur": 328, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751403167654233, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751403167654615, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751403167654907, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167655454, "dur": 621, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751403167656563, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167656977, "dur": 984, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751403167657961, "dur": 2097, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751403167660059, "dur": 4402, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751403167656771, "dur": 7758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167664529, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167665073, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167665330, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167665573, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167665956, "dur": 2198, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Utilities\\MathfEx.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751403167665799, "dur": 2420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167668221, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167668586, "dur": 2528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167671227, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167671526, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167673107, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167673385, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167674000, "dur": 1533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167675534, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167675620, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167675813, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167675869, "dur": 1756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167677659, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167677832, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167678042, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167678676, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167679104, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167679160, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167679995, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167680188, "dur": 1848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167682038, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167682156, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167682399, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167683391, "dur": 943, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167684336, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167684427, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167684642, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167685421, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167685552, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167685644, "dur": 5039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167690686, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751403167690936, "dur": 100340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167791278, "dur": 4852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167796177, "dur": 2436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167798654, "dur": 2922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167801578, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167801911, "dur": 2528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167804504, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167806938, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167807031, "dur": 4334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167811410, "dur": 2673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751403167814486, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751403167814668, "dur": 994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751403167815710, "dur": 5281127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167611177, "dur": 39341, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167650523, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167651140, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167651503, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_622A1FF3909771E4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167651602, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_42529D1BD0B2CBE0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167651693, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_42529D1BD0B2CBE0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167651797, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_40E8F641E9958680.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167651932, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167652500, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167652682, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167652972, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167653078, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167653147, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167653224, "dur": 342, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167653600, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751403167653968, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167654359, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167654463, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167654569, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167654851, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167655134, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167655429, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167656018, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12853064938409020984.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167656352, "dur": 557, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751403167656910, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167657180, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167657445, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167657857, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167658182, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167658385, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167658606, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167658849, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167659060, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167659275, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167659488, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167659702, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167659916, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167660152, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167660395, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167660676, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167660913, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167661136, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167661386, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167661593, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167661823, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167662673, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167662908, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167663127, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167663358, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167663571, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167663809, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167664028, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167664447, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\GetMemberOption.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751403167664263, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167664997, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167665387, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167665621, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167665929, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167666173, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167666437, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167666668, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167667080, "dur": 959, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_UpdateRegistery.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751403167667024, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167668200, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167668392, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167669015, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167669190, "dur": 1135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167670398, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167670794, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167670975, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167672122, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167672691, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167672990, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167673512, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167674065, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167674649, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167675385, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167675832, "dur": 524, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167676358, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167677226, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167677476, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167678568, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167679520, "dur": 1202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167680776, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167680949, "dur": 1074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167682024, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167682127, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167682518, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167682751, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167682989, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167684020, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167684371, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167684427, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167684781, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167684960, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167685019, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167685740, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167685817, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751403167686025, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167686618, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167686693, "dur": 73627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167760325, "dur": 31286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167791613, "dur": 3117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167794732, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751403167794804, "dur": 2799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167797650, "dur": 2389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167800076, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167800148, "dur": 2606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167802808, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167805089, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167807438, "dur": 5002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167812486, "dur": 2903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751403167815465, "dur": 5281399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167611232, "dur": 39305, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167650541, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_1AA5CC13572AAA47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167651079, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_91F787F420E8C88E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167651158, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FC3AA1C0027C0ADA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167651808, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_11834CDAF2387C83.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167651981, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_2856DC57C214A289.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167652140, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167652726, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_2A790542FCF7EA37.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167653134, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751403167653211, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167653300, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751403167653586, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751403167654047, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751403167654139, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751403167654496, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751403167654843, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167655119, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751403167655444, "dur": 706, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751403167656221, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751403167656381, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751403167656546, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751403167657045, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751403167656708, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167657666, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167658024, "dur": 1749, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-profile-l1-1-0.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751403167658013, "dur": 1939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167659952, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167660538, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167660770, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167660991, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167661218, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167661459, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167661828, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Interfaces\\IMayRequireViewDirection.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403167661785, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167662650, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167662863, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167663086, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167663310, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167663529, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167663757, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167663978, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167664207, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167664435, "dur": 1419, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.rendering.light-transport\\Runtime\\UnifiedRayTracing\\Compute\\BLASPositionsPool.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403167664435, "dur": 1712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167666148, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167666586, "dur": 1286, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Interface\\Annotations\\AnnotationDisabler.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403167666423, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167667927, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167668381, "dur": 1681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167670129, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167670369, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167670990, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167671070, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167671972, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167672056, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167672388, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167672928, "dur": 1786, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167674763, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167675021, "dur": 1298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167676325, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167676542, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167677205, "dur": 710, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\RenderGraph\\RenderGraphLogger.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403167676986, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167677957, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167678246, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167678456, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167679039, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Variables\\InspectorVariableNameAttribute.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403167678933, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167679712, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167680104, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167680310, "dur": 1731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167682042, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167682316, "dur": 1751, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\InvalidOperatorException.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751403167682143, "dur": 2321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167684465, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167684767, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167684960, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167685028, "dur": 1890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167687011, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167687221, "dur": 1025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167688333, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751403167688543, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167689099, "dur": 104048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167793150, "dur": 2322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167795474, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751403167795617, "dur": 6460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167802131, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167804509, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167807199, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167809898, "dur": 2169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167812121, "dur": 3487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751403167815673, "dur": 5281181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167611286, "dur": 39324, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167650742, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167650916, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167651073, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_78F0F79A0FB195D1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167651260, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167651366, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167651526, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0106BA1057A1C6CC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167651628, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167651711, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0106BA1057A1C6CC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167651909, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167652121, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_5911DF3198B3A870.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167652736, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403167653089, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403167653367, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403167653448, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167653518, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167654365, "dur": 513, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403167654889, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167655390, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403167656081, "dur": 321, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403167656403, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751403167656472, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167656647, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167656953, "dur": 995, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751403167657949, "dur": 2108, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.Messages.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751403167660057, "dur": 3715, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751403167656944, "dur": 6996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167663940, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167664177, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167664415, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167664649, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167664875, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167665194, "dur": 2844, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\LessOrEqual.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751403167665110, "dur": 3142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167668253, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167668461, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167669183, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167669423, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167670313, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167671288, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167671599, "dur": 6943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167678543, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167678719, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167678949, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167679197, "dur": 1526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167680780, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167680959, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167681598, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167681665, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167681727, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167681979, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167682440, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167682659, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167682892, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167683416, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167684028, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167684429, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167685458, "dur": 3570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167689030, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751403167689231, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167689772, "dur": 101512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167791285, "dur": 2375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167793661, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167793766, "dur": 2556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167796324, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167796502, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167798970, "dur": 4337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167803308, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167803408, "dur": 2469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167805924, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167808595, "dur": 2455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167811100, "dur": 2991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751403167814092, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167814664, "dur": 790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751403167815493, "dur": 5281346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167611332, "dur": 39858, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167651436, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_5DB2248094ADF2F7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167651927, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167652065, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167652170, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_805E31AC8FEB08D0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167652655, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751403167652817, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751403167653049, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751403167653456, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751403167653527, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167653703, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751403167654039, "dur": 367, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751403167654417, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167654848, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167655228, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167656081, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751403167656236, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751403167656561, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167656999, "dur": 765, "ph": "X", "name": "File", "args": {"detail": "E:\\Unity\\Editior\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751403167656782, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167657848, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167658097, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167658401, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.toonshader\\Editor\\Converter\\FromUTS3\\HdrpUTS3toIntegratedUTS3Converter.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751403167658257, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167658977, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167659182, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167659412, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167659627, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167659847, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167660378, "dur": 1917, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Serialization\\JsonObject.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751403167660182, "dur": 2180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167662363, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167662563, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167662761, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167662964, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167663175, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167663405, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167663753, "dur": 1456, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\LookDev\\DisplayWindow.DebugSidePanel.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751403167663620, "dur": 1662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167665282, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167665539, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167665767, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167665988, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167666196, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167666440, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167666667, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167667050, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167667291, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167667564, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167667865, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167667998, "dur": 668, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167668678, "dur": 9744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167678501, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167678727, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167679773, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167679979, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167680153, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167680816, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167681005, "dur": 3125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167684131, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167684755, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167684945, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751403167685005, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167685803, "dur": 71057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167758426, "dur": 320, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751403167758747, "dur": 1375, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1751403167760126, "dur": 180, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1751403167756862, "dur": 3453, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167760316, "dur": 30786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167791105, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167793668, "dur": 2450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167796120, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167796181, "dur": 2292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167798508, "dur": 2507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167801070, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167803334, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167803420, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167805734, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167806225, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167808906, "dur": 2335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167811296, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751403167814227, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751403167814655, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403167814896, "dur": 5279347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751403173094265, "dur": 428, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751403173094244, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751403173094718, "dur": 2010, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751403173096738, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167611391, "dur": 39539, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167651081, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_FE696A2A6E4C16EE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167651162, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_0D411EBC79DB1BBD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167651555, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167651723, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_13694A2D45790405.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167651913, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_69C31F9D75BAAB88.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167652890, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403167653600, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403167653761, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751403167653813, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403167653922, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167654007, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167654232, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751403167654322, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403167654611, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751403167654881, "dur": 582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167655463, "dur": 633, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403167656139, "dur": 716, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751403167656856, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167657159, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167657431, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167657777, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167658010, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167658216, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167658477, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167658666, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167659044, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167659331, "dur": 919, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\UniversalRenderPipelineAsset\\SerializedUniversalRenderPipelineAsset.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403167659295, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167660514, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167660713, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167660931, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167661136, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167661369, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167661562, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167661755, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167662427, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167662709, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167662922, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167663161, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167663409, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167663624, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167663847, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167664096, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167664329, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167664557, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167664784, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167665080, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167665339, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167665553, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167665792, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167666002, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167666258, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167666482, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167666736, "dur": 2474, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Debugging\\GraphDebugDataProvider.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403167666714, "dur": 2712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167669472, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167669674, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167670586, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403167671445, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167671668, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167671873, "dur": 9219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403167681199, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167681392, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403167682318, "dur": 1220, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InvokerBase.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751403167681996, "dur": 2092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167684377, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167684485, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167684803, "dur": 1217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167686022, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167686215, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167686328, "dur": 1510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403167687929, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167688128, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403167689025, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167689220, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403167689893, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167690077, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403167690575, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167690674, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751403167690924, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403167692173, "dur": 115, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403167693542, "dur": 1621103, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403169317086, "dur": 15649, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751403169316542, "dur": 16260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403169333120, "dur": 70, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403169334159, "dur": 3631609, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751403172967642, "dur": 19400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751403172967632, "dur": 20699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403172989691, "dur": 200, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751403172990379, "dur": 97169, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751403173094247, "dur": 486, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751403173094238, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751403173094748, "dur": 2052, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751403173105976, "dur": 1507, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5212, "tid": 717, "ts": 1751403173131112, "dur": 4286, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5212, "tid": 717, "ts": 1751403173135453, "dur": 2541, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5212, "tid": 717, "ts": 1751403173123865, "dur": 15291, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}