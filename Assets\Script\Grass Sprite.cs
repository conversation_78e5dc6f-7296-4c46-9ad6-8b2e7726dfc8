using UnityEngine;
using System.Collections.Generic;

[RequireComponent(typeof(Terrain))]
public class GrassGenerator : MonoBehaviour
{
    public int density = 2000; // how many grass blades to generate
    public float grassMinSize = 0.5f;
    public float grassMaxSize = 1.2f;

    public Texture2D grassTexture;
    public Material grassMaterial;
    public Mesh grassMesh;

    Terrain terrain;
    TerrainData terrainData;

    // Arrays for instanced rendering
    private Matrix4x4[] grassMatrices;
    private MaterialPropertyBlock propertyBlock;

    void Start()
    {
        terrain = GetComponent<Terrain>();
        terrainData = terrain.terrainData;
        propertyBlock = new MaterialPropertyBlock();

        GenerateGrass();
    }

    void GenerateGrass()
    {
        Vector3 terrainSize = terrainData.size;
        grassMatrices = new Matrix4x4[density];

        for (int i = 0; i < density; i++)
        {
            float posX = Random.Range(0, terrainSize.x);
            float posZ = Random.Range(0, terrainSize.z);
            float height = terrain.SampleHeight(new Vector3(posX, 0, posZ));

            Vector3 position = new Vector3(posX, height, posZ) + terrain.transform.position;

            // Random Y-axis rotation
            Quaternion rotation = Quaternion.Euler(0, Random.Range(0, 360f), 0);

            float scale = Random.Range(grassMinSize, grassMaxSize);
            Vector3 scaleVector = Vector3.one * scale;

            // Create transformation matrix
            grassMatrices[i] = Matrix4x4.TRS(position, rotation, scaleVector);
        }

        // Set texture in material property block
        if (grassTexture != null)
        {
            propertyBlock.SetTexture("_MainTex", grassTexture);
        }
    }

    void Update()
    {
        // Render all grass instances without creating GameObjects
        if (grassMesh != null && grassMaterial != null && grassMatrices != null)
        {
            Graphics.DrawMeshInstanced(
                grassMesh,
                0,
                grassMaterial,
                grassMatrices,
                grassMatrices.Length,
                propertyBlock,
                UnityEngine.Rendering.ShadowCastingMode.Off,
                false
            );
        }
    }
}
