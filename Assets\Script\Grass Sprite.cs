using UnityEngine;
using System.Collections.Generic;

[RequireComponent(typeof(Terrain))]
public class GrassGenerator : MonoBehaviour
{
    public int density = 2000; // how many grass blades to generate
    public float grassMinSize = 0.5f;
    public float grassMaxSize = 1.2f;

    public Texture2D grassTexture;
    public Material grassMaterial;
    public Mesh grassMesh;

    Terrain terrain;
    TerrainData terrainData;

    // Arrays for instanced rendering
    private Matrix4x4[] grassMatrices;
    private MaterialPropertyBlock propertyBlock;

    void Start()
    {
        terrain = GetComponent<Terrain>();
        terrainData = terrain.terrainData;
        propertyBlock = new MaterialPropertyBlock();

        GenerateGrass();
    }

    void GenerateGrass()
    {
        Vector3 terrainSize = terrainData.size;
        grassMatrices = new Matrix4x4[density];

        for (int i = 0; i < density; i++)
        {
            float posX = Random.Range(0, terrainSize.x);
            float posZ = Random.Range(0, terrainSize.z);

            Vector3 position = new Vector3(posX, 0.5f, posZ) + terrain.transform.position;

            float scale = Random.Range(grassMinSize, grassMaxSize);
            Vector3 scaleVector = Vector3.one * scale;

            // Create transformation matrix without rotation (billboarding will be handled in Update)
            grassMatrices[i] = Matrix4x4.TRS(position, Quaternion.identity, scaleVector);
        }

        // Set texture in material property block
        if (grassTexture != null)
        {
            propertyBlock.SetTexture("_MainTex", grassTexture);
        }
    }

    void Update()
    {
        // Update matrices for billboarding
        if (grassMatrices != null && Camera.main != null)
        {
            UpdateBillboardMatrices();
        }

        // Render all grass instances without creating GameObjects
        if (grassMesh != null && grassMaterial != null && grassMatrices != null)
        {
            Graphics.DrawMeshInstanced(
                grassMesh,
                0,
                grassMaterial,
                grassMatrices,
                grassMatrices.Length,
                propertyBlock,
                UnityEngine.Rendering.ShadowCastingMode.Off,
                false
            );
        }
    }

    void UpdateBillboardMatrices()
    {
        Vector3 cameraPosition = Camera.main.transform.position;
        Vector3 terrainSize = terrainData.size;

        for (int i = 0; i < grassMatrices.Length; i++)
        {
            // Extract position and scale from existing matrix
            Vector3 position = new Vector3(grassMatrices[i].m03, grassMatrices[i].m13, grassMatrices[i].m23);
            Vector3 scale = new Vector3(grassMatrices[i].m00, grassMatrices[i].m11, grassMatrices[i].m22);

            // Calculate billboard rotation (face camera on Y-axis only)
            Vector3 directionToCamera = (cameraPosition - position).normalized;
            directionToCamera.y = 0; // Keep grass upright
            Quaternion billboardRotation = Quaternion.LookRotation(directionToCamera);

            // Update matrix with billboard rotation
            grassMatrices[i] = Matrix4x4.TRS(position, billboardRotation, scale);
        }
    }
}
