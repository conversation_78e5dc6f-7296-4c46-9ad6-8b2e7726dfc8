using UnityEngine;
using System.Collections.Generic;

[RequireComponent(typeof(Terrain))]
public class GrassGenerator : MonoBehaviour
{
    public int density = 2000; // how many grass blades to generate
    public float grassMinSize = 0.5f;
    public float grassMaxSize = 1.2f;

    public Texture2D grassTexture;
    public Material grassMaterial;
    public Mesh grassMesh;

    Terrain terrain;
    TerrainData terrainData;

    // Arrays for instanced rendering
    private Matrix4x4[] grassMatrices;
    private MaterialPropertyBlock propertyBlock;

    // Store original positions and scales for billboarding
    private Vector3[] grassPositions;
    private float[] grassScales;

    void Start()
    {
        terrain = GetComponent<Terrain>();
        terrainData = terrain.terrainData;
        propertyBlock = new MaterialPropertyBlock();

        GenerateGrass();
    }

    void GenerateGrass()
    {
        Vector3 terrainSize = terrainData.size;
        grassMatrices = new Matrix4x4[density];
        grassPositions = new Vector3[density];
        grassScales = new float[density];

        Debug.Log($"Generating {density} grass instances on terrain size: {terrainSize}");

        for (int i = 0; i < density; i++)
        {
            float posX = Random.Range(0, terrainSize.x);
            float posZ = Random.Range(0, terrainSize.z);

            Vector3 position = new Vector3(posX, 0.5f, posZ) + terrain.transform.position;
            float scale = Random.Range(grassMinSize, grassMaxSize);

            // Store original data
            grassPositions[i] = position;
            grassScales[i] = scale;

            // Create initial transformation matrix
            grassMatrices[i] = Matrix4x4.TRS(position, Quaternion.identity, Vector3.one * scale);
        }

        // Set texture in material property block
        if (grassTexture != null)
        {
            propertyBlock.SetTexture("_MainTex", grassTexture);
        }

        Debug.Log($"Grass generation complete. Material: {grassMaterial != null}, Mesh: {grassMesh != null}, Texture: {grassTexture != null}");
    }

    void Update()
    {
        // Update matrices for billboarding
        if (grassMatrices != null && Camera.main != null)
        {
            UpdateBillboardMatrices();
        }

        // Render all grass instances without creating GameObjects
        if (grassMesh != null && grassMaterial != null && grassMatrices != null)
        {
            // Add bounds to ensure rendering
            Bounds bounds = new Bounds(terrain.transform.position, terrainData.size);

            Graphics.DrawMeshInstanced(
                grassMesh,
                0,
                grassMaterial,
                grassMatrices,
                grassMatrices.Length,
                propertyBlock,
                UnityEngine.Rendering.ShadowCastingMode.Off,
                false,
                0,
                Camera.main,
                UnityEngine.Rendering.LightProbeUsage.Off
            );
        }
        else
        {
            if (grassMesh == null) Debug.LogWarning("Grass mesh is null!");
            if (grassMaterial == null) Debug.LogWarning("Grass material is null!");
            if (grassMatrices == null) Debug.LogWarning("Grass matrices is null!");
        }
    }

    void UpdateBillboardMatrices()
    {
        Camera camera = Camera.main;
        if (camera == null) return;

        // Get camera's forward direction for orthographic billboarding
        Vector3 cameraForward = camera.transform.forward;

        for (int i = 0; i < grassMatrices.Length; i++)
        {
            // Use stored position and scale data
            Vector3 position = grassPositions[i];
            float scale = grassScales[i];

            // For orthographic camera, use camera's forward direction projected on XZ plane
            Vector3 billboardDirection = new Vector3(cameraForward.x, 0, cameraForward.z).normalized;

            // If the direction is too small, use a default direction
            if (billboardDirection.magnitude < 0.1f)
            {
                billboardDirection = Vector3.forward;
            }

            // Create rotation that faces the camera direction
            Quaternion billboardRotation = Quaternion.LookRotation(billboardDirection, Vector3.up);

            // Update matrix with billboard rotation
            grassMatrices[i] = Matrix4x4.TRS(position, billboardRotation, Vector3.one * scale);
        }
    }
}
