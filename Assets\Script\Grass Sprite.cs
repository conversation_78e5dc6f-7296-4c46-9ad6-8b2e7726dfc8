using UnityEngine;

[RequireComponent(typeof(Terrain))]
public class GrassParticleController : MonoBehaviour
{
    [Header("Mesh")]
    public Mesh grassMesh;

    [Header("Material")]
    public Material grassParticleMaterial;

    [Header("Layer")]
    public int layer = 11;

    [Header("Distribution")]
    public int dx = 10;
    public int dz = 10;
    public int scale = 16;
    public int yOffset = 8;

    [Header("Effects")]
    public float popoutRate = 0.01f;
    public float popoutDistance = 0.5f;

    private Terrain terrain;
    private TerrainData terrainData;
    private Matrix4x4[] grassMatrices;
    private Vector3[] grassPositions;
    private float[] grassScales;
    private int totalGrassCount;

    void Start()
    {
        terrain = GetComponent<Terrain>();
        terrainData = terrain.terrainData;

        GenerateGrass();
    }

    void GenerateGrass()
    {
        Vector3 terrainSize = terrainData.size;

        // Calculate total grass count based on dx and dz distribution
        int grassPerCellX = Mathf.Max(1, dx);
        int grassPerCellZ = Mathf.Max(1, dz);
        int cellsX = Mathf.Max(1, Mathf.FloorToInt(terrainSize.x / scale));
        int cellsZ = Mathf.Max(1, Mathf.FloorToInt(terrainSize.z / scale));
        totalGrassCount = cellsX * cellsZ * grassPerCellX * grassPerCellZ;

        grassMatrices = new Matrix4x4[totalGrassCount];
        grassPositions = new Vector3[totalGrassCount];
        grassScales = new float[totalGrassCount];

        Debug.Log($"Generating {totalGrassCount} grass instances on terrain size: {terrainSize}");
        Debug.Log($"Grid: {cellsX}x{cellsZ} cells, {grassPerCellX}x{grassPerCellZ} grass per cell, scale: {scale}");

        int grassIndex = 0;

        // Generate grass in a grid pattern based on dx, dz, and scale
        for (int cellX = 0; cellX < cellsX; cellX++)
        {
            for (int cellZ = 0; cellZ < cellsZ; cellZ++)
            {
                for (int gx = 0; gx < grassPerCellX; gx++)
                {
                    for (int gz = 0; gz < grassPerCellZ; gz++)
                    {
                        if (grassIndex >= totalGrassCount) break;

                        // Calculate position within cell
                        float cellPosX = cellX * scale + (gx + Random.Range(-0.3f, 0.3f)) * (scale / grassPerCellX);
                        float cellPosZ = cellZ * scale + (gz + Random.Range(-0.3f, 0.3f)) * (scale / grassPerCellZ);

                        // Clamp to terrain bounds
                        cellPosX = Mathf.Clamp(cellPosX, 0, terrainSize.x);
                        cellPosZ = Mathf.Clamp(cellPosZ, 0, terrainSize.z);

                        Vector3 position = new Vector3(cellPosX, yOffset * 0.1f, cellPosZ) + terrain.transform.position;
                        float grassScale = scale * 0.1f + Random.Range(-scale * 0.02f, scale * 0.02f);

                        // Store data
                        grassPositions[grassIndex] = position;
                        grassScales[grassIndex] = grassScale;

                        // Create initial matrix
                        grassMatrices[grassIndex] = Matrix4x4.TRS(position, Quaternion.identity, Vector3.one * grassScale);

                        grassIndex++;
                    }
                }
            }
        }

        Debug.Log($"Grass generation complete. Material: {grassParticleMaterial != null}, Mesh: {grassMesh != null}");
    }

    void Update()
    {
        // Update billboarding and effects
        if (grassMatrices != null && Camera.main != null)
        {
            UpdateGrassMatrices();
        }

        // Render grass
        RenderGrass();
    }

    void UpdateGrassMatrices()
    {
        Camera camera = Camera.main;
        if (camera == null) return;

        // Get camera's forward direction for orthographic billboarding
        Vector3 cameraForward = camera.transform.forward;
        Vector3 billboardDirection = new Vector3(cameraForward.x, 0, cameraForward.z).normalized;

        if (billboardDirection.magnitude < 0.1f)
        {
            billboardDirection = Vector3.forward;
        }

        // Calculate billboard rotation
        Quaternion billboardRotation = Quaternion.LookRotation(billboardDirection, Vector3.up);
        Vector3 cameraPos = camera.transform.position;

        for (int i = 0; i < grassMatrices.Length; i++)
        {
            Vector3 position = grassPositions[i];
            float baseScale = grassScales[i];

            // Apply popout effect
            float distance = Vector3.Distance(position, cameraPos);
            float scaleFactor = 1.0f;

            if (distance < popoutDistance)
            {
                scaleFactor = 1.0f + (popoutDistance - distance) * popoutRate;
            }

            float finalScale = baseScale * scaleFactor;
            grassMatrices[i] = Matrix4x4.TRS(position, billboardRotation, Vector3.one * finalScale);
        }
    }

    void RenderGrass()
    {
        if (grassMesh != null && grassMaterial != null && grassMatrices != null)
        {
            Graphics.DrawMeshInstanced(
                grassMesh,
                0,
                grassMaterial,
                grassMatrices,
                grassMatrices.Length,
                null,
                UnityEngine.Rendering.ShadowCastingMode.Off,
                false,
                0,
                Camera.main
            );
        }
        else
        {
            if (grassMesh == null) Debug.LogWarning("Grass mesh is null!");
            if (grassMaterial == null) Debug.LogWarning("Grass material is null!");
            if (grassMatrices == null) Debug.LogWarning("Grass matrices is null!");
        }
    }

    // Public method to regenerate grass
    public void RegenerateGrass()
    {
        GenerateGrass();
    }
}
