using UnityEngine;

[RequireComponent(typeof(Terrain))]
public class GrassParticleController : MonoBehaviour
{
    [Header("Mesh")]
    public Mesh grassMesh;

    [Header("Material")]
    public Material grassParticleMaterial;

    [Header("Layer")]
    public int layer = 11;

    [Header("Distribution")]
    public int dx = 10;
    public int dz = 10;
    public int scale = 16;
    public int yOffset = 8;

    [Header("Effects")]
    public float popoutRate = 0.01f;
    public float popoutDistance = 0.5f;

    private Terrain terrain;
    private TerrainData terrainData;
    private Matrix4x4[] grassMatrices;
    private Vector3[] grassPositions;
    private float[] grassScales;
    private int totalGrassCount;

    void Start()
    {
        terrain = GetComponent<Terrain>();
        terrainData = terrain.terrainData;

        GenerateGrass();
    }

    void GenerateGrass()
    {
        Vector3 terrainSize = terrainData.size;

        // Calculate total grass count based on dx and dz distances
        float distanceX = Mathf.Max(0.1f, dx);
        float distanceZ = Mathf.Max(0.1f, dz);

        // Calculate grass count to cover entire terrain (add 1 to ensure coverage)
        int grassCountX = Mathf.CeilToInt(terrainSize.x / distanceX) + 1;
        int grassCountZ = Mathf.CeilToInt(terrainSize.z / distanceZ) + 1;
        totalGrassCount = grassCountX * grassCountZ;

        grassMatrices = new Matrix4x4[totalGrassCount];
        grassPositions = new Vector3[totalGrassCount];
        grassScales = new float[totalGrassCount];

        Debug.Log($"Generating {totalGrassCount} grass instances on terrain size: {terrainSize}");
        Debug.Log($"Grid: {grassCountX}x{grassCountZ} grass, distance: {distanceX}x{distanceZ}, scale: {scale}");

        int grassIndex = 0;

        // Generate grass in a grid pattern to cover entire terrain
        for (int x = 0; x < grassCountX; x++)
        {
            for (int z = 0; z < grassCountZ; z++)
            {
                if (grassIndex >= totalGrassCount) break;

                // Calculate position based on distance, starting from edge
                float posX = x * distanceX + Random.Range(-distanceX * 0.2f, distanceX * 0.2f);
                float posZ = z * distanceZ + Random.Range(-distanceZ * 0.2f, distanceZ * 0.2f);

                // Ensure grass stays within terrain bounds
                posX = Mathf.Clamp(posX, 0, terrainSize.x - 0.1f);
                posZ = Mathf.Clamp(posZ, 0, terrainSize.z - 0.1f);

                Vector3 position = new Vector3(posX, yOffset * 0.1f, posZ) + terrain.transform.position;
                float grassScale = scale * 0.1f + Random.Range(-scale * 0.02f, scale * 0.02f);

                // Store data
                grassPositions[grassIndex] = position;
                grassScales[grassIndex] = grassScale;

                // Create initial matrix
                grassMatrices[grassIndex] = Matrix4x4.TRS(position, Quaternion.identity, Vector3.one * grassScale);

                grassIndex++;
            }
        }

        Debug.Log($"Grass generation complete. Total placed: {grassIndex}, Material: {grassParticleMaterial != null}, Mesh: {grassMesh != null}");
    }

    void Update()
    {
        // Update billboarding and effects
        if (grassMatrices != null && Camera.main != null)
        {
            UpdateGrassMatrices();
        }

        // Render grass
        RenderGrass();
    }

    void UpdateGrassMatrices()
    {
        Camera camera = Camera.main;
        if (camera == null) return;

        // Get camera's forward direction for orthographic billboarding
        Vector3 cameraForward = camera.transform.forward;
        Vector3 billboardDirection = new Vector3(cameraForward.x, 0, cameraForward.z).normalized;

        if (billboardDirection.magnitude < 0.1f)
        {
            billboardDirection = Vector3.forward;
        }

        // Calculate billboard rotation
        Quaternion billboardRotation = Quaternion.LookRotation(billboardDirection, Vector3.up);
        Vector3 cameraPos = camera.transform.position;

        for (int i = 0; i < grassMatrices.Length; i++)
        {
            Vector3 position = grassPositions[i];
            float baseScale = grassScales[i];

            // Apply popout effect
            float distance = Vector3.Distance(position, cameraPos);
            float scaleFactor = 1.0f;

            if (distance < popoutDistance)
            {
                scaleFactor = 1.0f + (popoutDistance - distance) * popoutRate;
            }

            float finalScale = baseScale * scaleFactor;
            grassMatrices[i] = Matrix4x4.TRS(position, billboardRotation, Vector3.one * finalScale);
        }
    }

    void RenderGrass()
    {
        if (grassMesh != null && grassParticleMaterial != null && grassMatrices != null)
        {
            Graphics.DrawMeshInstanced(
                grassMesh,
                0,
                grassParticleMaterial,
                grassMatrices,
                grassMatrices.Length,
                null,
                UnityEngine.Rendering.ShadowCastingMode.Off,
                false,
                layer,
                Camera.main
            );
        }
        else
        {
            if (grassMesh == null) Debug.LogWarning("Grass mesh is null!");
            if (grassParticleMaterial == null) Debug.LogWarning("Grass particle material is null!");
            if (grassMatrices == null) Debug.LogWarning("Grass matrices is null!");
        }
    }

    // Public method to regenerate grass
    public void RegenerateGrass()
    {
        GenerateGrass();
    }
}
