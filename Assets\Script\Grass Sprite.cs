using UnityEngine;

[RequireComponent(typeof(Terrain), typeof(ParticleSystem))]
public class GrassParticleController : MonoBehaviour
{
    [Header("Grass Settings")]
    public int density = 2000;
    public float grassMinSize = 0.5f;
    public float grassMaxSize = 1.2f;
    public float yOffset = 0.5f;

    [Header("Particle Settings")]
    public Material grassParticleMaterial;
    public Mesh grassMesh;

    [Header("Distribution")]
    public float popoutRate = 0.01f;
    public float popoutDistance = 0.5f;

    private Terrain terrain;
    private TerrainData terrainData;
    private ParticleSystem grassParticleSystem;
    private ParticleSystem.Particle[] particles;

    void Start()
    {
        terrain = GetComponent<Terrain>();
        terrainData = terrain.terrainData;
        grassParticleSystem = GetComponent<ParticleSystem>();

        SetupParticleSystem();
        GenerateGrassParticles();
    }

    void SetupParticleSystem()
    {
        var main = grassParticleSystem.main;
        main.startLifetime = Mathf.Infinity;
        main.startSpeed = 0;
        main.maxParticles = density;
        main.simulationSpace = ParticleSystemSimulationSpace.World;
        main.startSize3D = true;
        main.startSizeX = grassMinSize;
        main.startSizeY = grassMinSize;
        main.startSizeZ = grassMinSize;

        var emission = grassParticleSystem.emission;
        emission.enabled = false;

        var shape = grassParticleSystem.shape;
        shape.enabled = false;

        // Set up mesh renderer
        var renderer = grassParticleSystem.GetComponent<ParticleSystemRenderer>();
        if (renderer != null)
        {
            renderer.material = grassParticleMaterial;
            renderer.mesh = grassMesh;
            renderer.alignment = ParticleSystemRenderSpace.Facing;
        }
    }

    void GenerateGrassParticles()
    {
        Vector3 terrainSize = terrainData.size;
        particles = new ParticleSystem.Particle[density];

        Debug.Log($"Generating {density} grass particles on terrain size: {terrainSize}");

        for (int i = 0; i < density; i++)
        {
            float posX = Random.Range(0, terrainSize.x);
            float posZ = Random.Range(0, terrainSize.z);

            Vector3 position = new Vector3(posX, yOffset, posZ) + terrain.transform.position;
            float scale = Random.Range(grassMinSize, grassMaxSize);

            particles[i].position = position;
            particles[i].startSize3D = new Vector3(scale, scale, scale);
            particles[i].startColor = Color.white;
            particles[i].remainingLifetime = Mathf.Infinity;
            particles[i].velocity = Vector3.zero;

            // Random rotation for variety
            particles[i].rotation3D = new Vector3(0, Random.Range(0, 360f), 0);
        }

        grassParticleSystem.SetParticles(particles, particles.Length);
        Debug.Log($"Grass particle generation complete. Material: {grassParticleMaterial != null}, Mesh: {grassMesh != null}");
    }

    void Update()
    {
        // Update billboarding for orthographic camera
        if (particles != null && Camera.main != null)
        {
            UpdateParticleBillboarding();
        }

        // Apply popout effect
        ApplyPopoutEffect();
    }

    void UpdateParticleBillboarding()
    {
        Camera camera = Camera.main;
        if (camera == null) return;

        // Get camera's forward direction for orthographic billboarding
        Vector3 cameraForward = camera.transform.forward;
        Vector3 billboardDirection = new Vector3(cameraForward.x, 0, cameraForward.z).normalized;

        if (billboardDirection.magnitude < 0.1f)
        {
            billboardDirection = Vector3.forward;
        }

        // Calculate billboard rotation
        float yRotation = Mathf.Atan2(billboardDirection.x, billboardDirection.z) * Mathf.Rad2Deg;

        // Update all particles to face camera
        for (int i = 0; i < particles.Length; i++)
        {
            particles[i].rotation3D = new Vector3(0, yRotation, 0);
        }

        grassParticleSystem.SetParticles(particles, particles.Length);
    }

    void ApplyPopoutEffect()
    {
        if (Camera.main == null || particles == null) return;

        Vector3 cameraPos = Camera.main.transform.position;
        bool needsUpdate = false;

        for (int i = 0; i < particles.Length; i++)
        {
            float distance = Vector3.Distance(particles[i].position, cameraPos);

            if (distance < popoutDistance)
            {
                // Scale up when close to camera
                float scaleFactor = 1.0f + (popoutDistance - distance) * popoutRate;
                Vector3 originalSize = new Vector3(
                    Random.Range(grassMinSize, grassMaxSize),
                    Random.Range(grassMinSize, grassMaxSize),
                    Random.Range(grassMinSize, grassMaxSize)
                );
                particles[i].startSize3D = originalSize * scaleFactor;
                needsUpdate = true;
            }
        }

        if (needsUpdate)
        {
            grassParticleSystem.SetParticles(particles, particles.Length);
        }
    }

    // Public method to regenerate grass
    public void RegenerateGrass()
    {
        GenerateGrassParticles();
    }
}
